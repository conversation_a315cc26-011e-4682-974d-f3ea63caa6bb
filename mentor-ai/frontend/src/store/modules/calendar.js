// store/modules/calendar.js
import axios from 'axios'

// API service setup para comunicação com o backend
const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true // Para incluir cookies de autenticação
})

// Função utilitária para converter datas
const convertDateFields = (event) => {
  if (!event) return null;
  
  return {
    ...event,
    start: typeof event.start === 'string' ? new Date(event.start) : event.start,
    end: typeof event.end === 'string' ? new Date(event.end) : event.end
  };
}

// Sample events for demo/offline mode
const sampleEvents = [
  {
    id: 'sample-1',
    title: 'Anatomia - Sistema Cardiovascular [▰]',
    start: new Date(new Date().setHours(9, 0, 0, 0)),
    end: new Date(new Date().setHours(10, 30, 0, 0)),
    type: 'study',
    subject: 'Anatomia',
    difficulty: 'easy',
    description: 'Estudo inicial do sistema cardiovascular'
  },
  {
    id: 'sample-2',
    title: 'Farmacologia - Antibióticos [▰▰]',
    start: new Date(new Date().setHours(14, 0, 0, 0)),
    end: new Date(new Date().setHours(15, 30, 0, 0)),
    type: 'revision',
    subject: 'Farmacologia',
    difficulty: 'medium',
    description: 'Primeira revisão de antibióticos'
  },
  {
    id: 'sample-3',
    title: 'Fisiologia - Respiração [▰▰▰]',
    start: new Date(new Date(Date.now() + 24 * 60 * 60 * 1000).setHours(10, 0, 0, 0)),
    end: new Date(new Date(Date.now() + 24 * 60 * 60 * 1000).setHours(11, 30, 0, 0)),
    type: 'revision',
    subject: 'Fisiologia',
    difficulty: 'hard',
    description: 'Revisão avançada do sistema respiratório'
  }
];

const state = {
  events: [],
  subjects: [],
  currentView: 'month',
  selectedDay: new Date(),
  isOnline: true, // Status de conexão
  pendingEvents: [], // Eventos a serem sincronizados quando ficar online
  hasInitialized: false, // Flag para controlar inicialização
  isLoading: false,
  error: null
}

const getters = {
  getEventsByDate: (state) => (date) => {
    return state.events.filter(event => {
      const eventDate = typeof event.start === 'string' ? 
        new Date(event.start) : event.start
      return eventDate.toISOString().split('T')[0] === date.toISOString().split('T')[0]
    })
  },
  getAllEvents: (state) => state.events,
  getSubjects: (state) => state.subjects,
  getCurrentView: (state) => state.currentView,
  getSelectedDay: (state) => state.selectedDay,
  getPendingEvents: (state) => state.pendingEvents,
  isOnline: (state) => state.isOnline
}

const actions = {
  // Inicializar o calendário
  async initializeCalendar({ commit, dispatch, state }) {
    if (state.hasInitialized) {
      return;
    }

    console.log('Inicializando calendário...');

    // Carregar eventos pendentes do localStorage
    const pendingEvents = JSON.parse(localStorage.getItem('pending_events') || '[]');
    if (pendingEvents.length > 0) {
      commit('LOAD_PENDING_EVENTS', pendingEvents);
    }

    // Tentar carregar eventos para o mês atual
    const today = new Date();
    const start = new Date(today.getFullYear(), today.getMonth(), 1);
    const end = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    await dispatch('fetchEvents', {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    });
  },

  // Sinalizar status de conexão
  setOnlineStatus({ commit, dispatch }, isOnline) {
    commit('SET_ONLINE_STATUS', isOnline);

    // Se estiver online novamente, tentar enviar eventos pendentes
    if (isOnline && state.pendingEvents.length > 0) {
      return dispatch('syncPendingEvents');
    }
  },
  
  // Sincronizar eventos pendentes
  async syncPendingEvents({ commit, state, dispatch }) {
    if (!state.isOnline || state.pendingEvents.length === 0) return;
    
    const pendingEvents = [...state.pendingEvents];
    commit('CLEAR_PENDING_EVENTS');
    
    for (const event of pendingEvents) {
      try {
        const { type, data } = event;
        
        if (type === 'add') {
          await api.post('/events/', data);
        } else if (type === 'update') {
          await api.put(`/events/${data.id}/`, data);
        } else if (type === 'delete') {
          await api.delete(`/events/${data}/`);
        } else if (type === 'complete') {
          await api.post('/events/complete_revision/', { id: data });
        }
      } catch (error) {
        console.error('Erro ao sincronizar evento pendente:', error);
        // Adicionar de volta à fila de pendências
        commit('ADD_PENDING_EVENT', event);
      }
    }
    
    // Recarregar eventos após sincronização
    try {
      await dispatch('fetchEventsFromServer');
    } catch (error) {
      console.error('Erro ao recarregar eventos após sincronização:', error);
    }
  },
  
  // Buscar eventos do servidor pelo intervalo de datas
  async fetchEvents({ commit, state }, { start, end }) {
    commit('SET_LOADING', true);
    commit('SET_ERROR', null);

    try {
      console.log(`Buscando eventos entre ${start} e ${end}`);

      if (state.isOnline) {
        try {
          // Tenta obter do servidor
          const response = await api.get(`/events/by_date_range/?start=${start}&end=${end}`);

          // Converter datas para objetos Date para manipulação mais fácil
          const events = response.data.map(event => convertDateFields(event));

          commit('SET_EVENTS', events);
          commit('SET_INITIALIZED', true);

          // Armazenar no localStorage como backup
          localStorage.setItem('calendar_events', JSON.stringify(events));

          return events;
        } catch (apiError) {
          console.warn('API não disponível, usando modo offline:', apiError.message);
          commit('SET_ONLINE_STATUS', false);
          throw apiError; // Re-throw para cair no catch principal
        }
      } else {
        // Offline: usar os eventos em memória ou localStorage
        console.log('Modo offline: usando eventos salvos');
        const storedEvents = JSON.parse(localStorage.getItem('calendar_events') || '[]');

        // Converter datas para objetos Date (localStorage armazena como strings)
        const events = storedEvents.map(event => convertDateFields(event));

        commit('SET_EVENTS', events);
        commit('SET_INITIALIZED', true);
        return events;
      }
    } catch (error) {
      console.error('Erro ao buscar eventos:', error);
      commit('SET_ERROR', error.message);

      // Em caso de erro na conexão, usar localStorage ou dados de exemplo
      console.log('Erro de conexão: tentando usar localStorage ou dados de exemplo');
      const storedEvents = JSON.parse(localStorage.getItem('calendar_events') || '[]');

      let events = [];
      if (storedEvents.length > 0) {
        // Usar eventos salvos
        events = storedEvents.map(event => convertDateFields(event));
      } else if (!state.hasInitialized) {
        // Se nunca foi inicializado, usar dados de exemplo
        console.log('Carregando dados de exemplo para demonstração');
        events = sampleEvents.map(event => ({ ...event })); // Clone dos dados de exemplo

        // Salvar dados de exemplo no localStorage para próximas sessões
        localStorage.setItem('calendar_events', JSON.stringify(events));
      }

      commit('SET_EVENTS', events);
      commit('SET_ONLINE_STATUS', false);
      commit('SET_INITIALIZED', true);

      return events;
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // Buscar todos os eventos (usado na inicialização)
  async fetchEventsFromServer({ commit, state }) {
    try {
      if (!state.isOnline) {
        console.log('Modo offline: não é possível buscar do servidor');
        return [];
      }
      
      const response = await api.get('/events/');
      
      // Converter datas para objetos Date
      const events = response.data.map(event => convertDateFields(event));
      
      commit('SET_EVENTS', events);
      
      // Backup no localStorage
      localStorage.setItem('calendar_events', JSON.stringify(events));
      
      return events;
    } catch (error) {
      console.error('Erro ao buscar todos os eventos:', error);
      commit('SET_ONLINE_STATUS', false);
      return [];
    }
  },
  
  async fetchSubjects({ commit, state }) {
    try {
      if (state.isOnline) {
        // Buscar do servidor
        const response = await api.get('/subjects/');
        const subjects = response.data;
        
        commit('SET_SUBJECTS', subjects);
        
        // Backup no localStorage
        localStorage.setItem('calendar_subjects', JSON.stringify(subjects));
        
        return subjects;
      } else {
        // Modo offline: usar dados armazenados
        const storedSubjects = JSON.parse(localStorage.getItem('calendar_subjects') || '[]');
        
        if (storedSubjects.length === 0) {
          // Fallback para dados mockados se não houver dados armazenados
          const mockSubjects = [
            { id: 1, name: 'Anatomia', color: '#e63946' },
            { id: 2, name: 'Fisiologia', color: '#457b9d' },
            { id: 3, name: 'Bioquímica', color: '#1d3557' },
            { id: 4, name: 'Farmacologia', color: '#f1faee' },
            { id: 5, name: 'Patologia', color: '#a8dadc' }
          ];
          
          commit('SET_SUBJECTS', mockSubjects);
          return mockSubjects;
        }
        
        commit('SET_SUBJECTS', storedSubjects);
        return storedSubjects;
      }
    } catch (error) {
      console.error('Erro ao buscar disciplinas:', error);
      
      // Fallback para dados mockados em caso de erro
      const mockSubjects = [
        { id: 1, name: 'Anatomia', color: '#e63946' },
        { id: 2, name: 'Fisiologia', color: '#457b9d' },
        { id: 3, name: 'Bioquímica', color: '#1d3557' },
        { id: 4, name: 'Farmacologia', color: '#f1faee' },
        { id: 5, name: 'Patologia', color: '#a8dadc' }
      ];
      
      commit('SET_SUBJECTS', mockSubjects);
      commit('SET_ONLINE_STATUS', false);
      
      return mockSubjects;
    }
  },
  
  async addEvent({ commit, state }, event) {
    try {
      console.log('Adicionando evento:', event);
      
      // Normalizar dados do evento
      const normalizedEvent = {
        ...event,
        id: event.id || `local_${Date.now()}`,
        start: typeof event.start === 'string' ? event.start : event.start.toISOString(),
        end: typeof event.end === 'string' ? event.end : event.end.toISOString()
      };
      
      // Adicionar ao store imediatamente para feedback rápido
      commit('ADD_EVENT', convertDateFields(normalizedEvent));
      
      // Atualizar cache local
      const storedEvents = JSON.parse(localStorage.getItem('calendar_events') || '[]');
      localStorage.setItem('calendar_events', JSON.stringify([...storedEvents, normalizedEvent]));
      
      if (state.isOnline) {
        // Enviar para o servidor se estiver online
        const response = await api.post('/events/', normalizedEvent);
        
        // Substituir o evento local pelo retornado do servidor
        commit('REPLACE_EVENT', { 
          oldId: normalizedEvent.id, 
          newEvent: convertDateFields(response.data) 
        });
        
        // Atualizar o localStorage
        const updatedEvents = state.events.map(e => {
          // Converter datas de volta para string para storage
          return {
            ...e,
            start: typeof e.start === 'object' ? e.start.toISOString() : e.start,
            end: typeof e.end === 'object' ? e.end.toISOString() : e.end
          };
        });
        localStorage.setItem('calendar_events', JSON.stringify(updatedEvents));
        
        return convertDateFields(response.data);
      } else {
        // Se estiver offline, adicionar à fila de pendências
        commit('ADD_PENDING_EVENT', { 
          type: 'add', 
          data: normalizedEvent 
        });
        
        return convertDateFields(normalizedEvent);
      }
    } catch (error) {
      console.error('Erro ao adicionar evento:', error);
      
      // Se falhar, adicionar à fila de pendências
      commit('ADD_PENDING_EVENT', { 
        type: 'add', 
        data: {
          ...event,
          start: typeof event.start === 'string' ? event.start : event.start.toISOString(),
          end: typeof event.end === 'string' ? event.end : event.end.toISOString()
        }
      });
      
      throw error;
    }
  },
  
  async updateEvent({ commit, state }, event) {
    try {
      // Normalizar dados do evento
      const normalizedEvent = {
        ...event,
        start: typeof event.start === 'string' ? event.start : event.start.toISOString(),
        end: typeof event.end === 'string' ? event.end : event.end.toISOString()
      };
      
      // Atualizar no store para feedback imediato
      commit('UPDATE_EVENT', convertDateFields(normalizedEvent));
      
      // Atualizar no localStorage
      const storedEvents = JSON.parse(localStorage.getItem('calendar_events') || '[]');
      const updatedEvents = storedEvents.map(e => 
        e.id === normalizedEvent.id ? normalizedEvent : e
      );
      localStorage.setItem('calendar_events', JSON.stringify(updatedEvents));
      
      if (state.isOnline && !normalizedEvent.id.startsWith('local_')) {
        // Enviar para o servidor
        const response = await api.put(`/events/${normalizedEvent.id}/`, normalizedEvent);
        return convertDateFields(response.data);
      } else {
        // Adicionar à fila de pendências
        commit('ADD_PENDING_EVENT', { 
          type: 'update', 
          data: normalizedEvent 
        });
        
        return convertDateFields(normalizedEvent);
      }
    } catch (error) {
      console.error('Erro ao atualizar evento:', error);
      
      // Adicionar à fila de pendências em caso de erro
      commit('ADD_PENDING_EVENT', { 
        type: 'update', 
        data: {
          ...event,
          start: typeof event.start === 'string' ? event.start : event.start.toISOString(),
          end: typeof event.end === 'string' ? event.end : event.end.toISOString()
        }
      });
      
      throw error;
    }
  },
  
  async deleteEvent({ commit, state }, eventId) {
    try {
      // Remover do store imediatamente
      commit('DELETE_EVENT', eventId);
      
      // Atualizar localStorage
      const storedEvents = JSON.parse(localStorage.getItem('calendar_events') || '[]');
      localStorage.setItem('calendar_events', 
        JSON.stringify(storedEvents.filter(e => e.id !== eventId))
      );
      
      if (state.isOnline && !eventId.startsWith('local_')) {
        // Remover do servidor
        await api.delete(`/events/${eventId}/`);
      } else {
        // Adicionar à fila de pendências
        commit('ADD_PENDING_EVENT', { 
          type: 'delete', 
          data: eventId 
        });
      }
    } catch (error) {
      console.error('Erro ao excluir evento:', error);
      
      // Adicionar à fila de pendências em caso de erro
      commit('ADD_PENDING_EVENT', { 
        type: 'delete', 
        data: eventId 
      });
      
      throw error;
    }
  },
  
  async completeRevision({ commit, state }, event) {
    try {
      // Atualizar no store imediatamente
      const updatedEvent = { ...event, progress: 100, completed: true };
      commit('UPDATE_EVENT', updatedEvent);
      
      // Atualizar localStorage
      const storedEvents = JSON.parse(localStorage.getItem('calendar_events') || '[]');
      const updatedEvents = storedEvents.map(e => 
        e.id === event.id ? { ...e, progress: 100, completed: true } : e
      );
      localStorage.setItem('calendar_events', JSON.stringify(updatedEvents));
      
      if (state.isOnline && !event.id.startsWith('local_')) {
        // Enviar para o servidor
        const response = await api.post('/events/complete_revision/', { id: event.id });
        return convertDateFields(response.data);
      } else {
        // Adicionar à fila de pendências
        commit('ADD_PENDING_EVENT', { 
          type: 'complete', 
          data: event.id 
        });
        
        return updatedEvent;
      }
    } catch (error) {
      console.error('Erro ao completar revisão:', error);
      
      // Adicionar à fila de pendências em caso de erro
      commit('ADD_PENDING_EVENT', { 
        type: 'complete', 
        data: event.id 
      });
      
      throw error;
    }
  },
  
  async createSubject({ commit, state }, subject) {
    try {
      if (state.isOnline) {
        const response = await api.post('/subjects/', subject);
        commit('ADD_SUBJECT', response.data);
        
        // Atualizar localStorage
        const subjects = state.subjects;
        localStorage.setItem('calendar_subjects', JSON.stringify(subjects));
        
        return response.data;
      } else {
        // Modo offline
        const newSubject = {
          id: `local_subject_${Date.now()}`,
          ...subject,
          created_at: new Date().toISOString()
        };
        
        commit('ADD_SUBJECT', newSubject);
        
        // Salvar no localStorage
        const subjects = state.subjects;
        localStorage.setItem('calendar_subjects', JSON.stringify(subjects));
        
        return newSubject;
      }
    } catch (error) {
      console.error('Erro ao criar disciplina:', error);
      throw error;
    }
  },
  
  async updateSubject({ commit, state }, { id, ...updates }) {
    try {
      if (state.isOnline && !id.toString().startsWith('local_')) {
        const response = await api.put(`/subjects/${id}/`, updates);
        commit('UPDATE_SUBJECT', response.data);
        
        // Atualizar localStorage
        const subjects = state.subjects;
        localStorage.setItem('calendar_subjects', JSON.stringify(subjects));
        
        return response.data;
      } else {
        // Modo offline
        const updatedSubject = { id, ...updates };
        commit('UPDATE_SUBJECT', updatedSubject);
        
        // Salvar no localStorage
        const subjects = state.subjects;
        localStorage.setItem('calendar_subjects', JSON.stringify(subjects));
        
        return updatedSubject;
      }
    } catch (error) {
      console.error('Erro ao atualizar disciplina:', error);
      throw error;
    }
  },
  
  async deleteSubject({ commit, state }, subjectId) {
    try {
      if (state.isOnline && !subjectId.toString().startsWith('local_')) {
        await api.delete(`/subjects/${subjectId}/`);
      }
      
      commit('DELETE_SUBJECT', subjectId);
      
      // Atualizar localStorage
      const subjects = state.subjects;
      localStorage.setItem('calendar_subjects', JSON.stringify(subjects));
      
    } catch (error) {
      console.error('Erro ao deletar disciplina:', error);
      throw error;
    }
  },
  
  async generateSpacedRepetition({ commit, state, dispatch }, form) {
    try {
      if (state.isOnline) {
        // Gerar via API do servidor
        const response = await api.post('/events/generate_spaced_repetition/', form);
        
        // Converter datas e adicionar ao store
        const events = response.data.map(event => convertDateFields(event));
        commit('ADD_EVENTS', events);
        
        // Atualizar localStorage
        const storedEvents = JSON.parse(localStorage.getItem('calendar_events') || '[]');
        localStorage.setItem('calendar_events', JSON.stringify([...storedEvents, ...response.data]));
        
        return events;
      } else {
        // Em modo offline, usar a implementação local temporária
        console.log('Gerando revisões espaçadas em modo offline (temporário)');
        
        const { subject, difficulty, startDate, revisionType } = form;
        
        // Definir intervalos com base na dificuldade
        const intervals = {
          'Fácil': [1, 3, 7, 14, 30],
          'Médio': [1, 2, 5, 10, 21],
          'Difícil': [1, 2, 3, 7, 14, 21, 30]
        }[difficulty] || [1, 3, 7, 14, 30];
        
        const baseDate = new Date(startDate);
        
        // Gerar eventos para cada intervalo
        const generatedEvents = [];
        
        for (let i = 0; i < intervals.length; i++) {
          const days = intervals[i];
          const eventDate = new Date(baseDate);
          eventDate.setDate(eventDate.getDate() + days);
          
          const newEvent = {
            id: `local_sr_${Date.now()}_${i}`,
            title: `Revisão ${i + 1}: ${subject}`,
            start: new Date(eventDate.setHours(9, 0, 0, 0)).toISOString(),
            end: new Date(eventDate.setHours(10, 0, 0, 0)).toISOString(),
            subject,
            description: `Revisão espaçada ${i + 1} para ${subject}`,
            color: '#42b983',
            isRevision: true,
            revisionType,
            priority: i < 2 ? 'Alta' : (i < 4 ? 'Média' : 'Baixa'),
            progress: 0,
            completed: false
          };
          
          // Adicionar cada evento individualmente
          await dispatch('addEvent', newEvent);
          generatedEvents.push(convertDateFields(newEvent));
        }
        
        return generatedEvents;
      }
    } catch (error) {
      console.error('Erro ao gerar revisões espaçadas:', error);
      throw error;
    }
  },
  
  setCalendarView({ commit }, view) {
    commit('SET_CALENDAR_VIEW', view);
  },
  
  setSelectedDay({ commit }, day) {
    commit('SET_SELECTED_DAY', day);
  }
}

const mutations = {
  SET_EVENTS(state, events) {
    state.events = events;
  },
  
  SET_SUBJECTS(state, subjects) {
    state.subjects = subjects;
  },
  
  ADD_SUBJECT(state, subject) {
    state.subjects.push(subject);
  },
  
  UPDATE_SUBJECT(state, updatedSubject) {
    const index = state.subjects.findIndex(s => s.id === updatedSubject.id);
    if (index !== -1) {
      state.subjects.splice(index, 1, updatedSubject);
    }
  },
  
  DELETE_SUBJECT(state, subjectId) {
    state.subjects = state.subjects.filter(s => s.id !== subjectId);
  },
  
  ADD_EVENT(state, event) {
    state.events.push(event);
  },
  
  ADD_EVENTS(state, events) {
    state.events = [...state.events, ...events];
  },
  
  UPDATE_EVENT(state, updatedEvent) {
    const index = state.events.findIndex(e => e.id === updatedEvent.id);
    if (index !== -1) {
      state.events.splice(index, 1, updatedEvent);
    }
  },
  
  REPLACE_EVENT(state, { oldId, newEvent }) {
    const index = state.events.findIndex(e => e.id === oldId);
    if (index !== -1) {
      state.events.splice(index, 1, newEvent);
    }
  },
  
  DELETE_EVENT(state, eventId) {
    state.events = state.events.filter(e => e.id !== eventId);
  },
  
  SET_CALENDAR_VIEW(state, view) {
    state.currentView = view;
  },
  
  SET_SELECTED_DAY(state, day) {
    state.selectedDay = day;
  },
  
  SET_ONLINE_STATUS(state, isOnline) {
    state.isOnline = isOnline;
  },
  
  ADD_PENDING_EVENT(state, pendingEvent) {
    state.pendingEvents.push(pendingEvent);
    // Salvar também no localStorage
    localStorage.setItem('pending_events', JSON.stringify(state.pendingEvents));
  },
  
  CLEAR_PENDING_EVENTS(state) {
    state.pendingEvents = [];
    localStorage.removeItem('pending_events');
  },
  
  LOAD_PENDING_EVENTS(state, events) {
    state.pendingEvents = events;
  },

  SET_INITIALIZED(state, value) {
    state.hasInitialized = value;
  },

  SET_LOADING(state, value) {
    state.isLoading = value;
  },

  SET_ERROR(state, error) {
    state.error = error;
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}