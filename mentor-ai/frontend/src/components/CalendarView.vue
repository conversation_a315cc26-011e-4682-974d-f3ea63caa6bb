<template>
  <div class="calendar-view">
    <!-- Modern Background Effects -->
    <div class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <div class="grid-overlay"></div>
    </div>

    <!-- Professional Header -->
    <div class="calendar-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="page-info">
            <h1 class="page-title">Calendário de Revisões</h1>
            <p class="page-subtitle">Organize e acompanhe seus estudos</p>
          </div>
        </div>

        <div class="header-right">
          <div class="date-navigation">
            <button @click="previousPeriod" class="nav-button">
              <i class="fas fa-chevron-left"></i>
            </button>
            <div class="current-period">
              <i class="far fa-calendar"></i>
              {{ formattedCurrentPeriod }}
            </div>
            <button @click="nextPeriod" class="nav-button">
              <i class="fas fa-chevron-right"></i>
            </button>
            <button @click="goToToday" class="today-button">
              <i class="fas fa-calendar-day"></i>
              Hoje
            </button>
          </div>
        </div>
      </div>

      <!-- Modern View Switcher -->
      <div class="view-switcher">
        <div class="view-tabs">
          <button 
            v-for="view in viewOptions" 
            :key="view.id"
            @click="switchView(view.id)" 
            :class="{ active: currentView === view.id }" 
            class="view-tab"
          >
            <i :class="view.icon"></i>
            <span>{{ view.label }}</span>
          </button>
          <div class="tab-indicator" :style="tabIndicatorStyle"></div>
        </div>

        <div class="action-buttons">
          <button @click="toggleFilters" class="action-button filters-button" :class="{ active: showFilters }">
            <i class="fas fa-filter"></i>
            <span>Filtros</span>
            <span v-if="activeFiltersCount > 0" class="badge">{{ activeFiltersCount }}</span>
          </button>
          
          <button @click="handleNewRevision" class="action-button primary-button">
            <i class="fas fa-plus"></i>
            <span>Nova Revisão</span>
          </button>
          
          <button @click="showSubjectModal = true" class="action-button">
            <i class="fas fa-folder-plus"></i>
            <span>Disciplinas</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Professional Filters Panel -->
    <transition name="filters-slide">
      <div v-if="showFilters" class="filters-panel">
        <div class="filters-content">
          <div class="filter-section">
            <h3 class="filter-title">
              <i class="fas fa-tag"></i>
              Tipo de Revisão
            </h3>
            <div class="filter-chips">
              <button 
                v-for="type in revisionTypes" 
                :key="type"
                @click="toggleFilter('type', type)"
                :class="{ active: filters.type.includes(type) }"
                class="filter-chip"
              >
                <i :class="getRevisionIcon(type)"></i>
                {{ type }}
              </button>
            </div>
          </div>

          <div class="filter-section">
            <h3 class="filter-title">
              <i class="fas fa-exclamation-circle"></i>
              Prioridade
            </h3>
            <div class="filter-chips">
              <button 
                v-for="priority in priorities" 
                :key="priority"
                @click="toggleFilter('priority', priority)"
                :class="{ active: filters.priority.includes(priority) }"
                class="filter-chip priority-chip"
                :data-priority="priority.toLowerCase()"
              >
                <span class="priority-indicator"></span>
                {{ priority }}
              </button>
            </div>
          </div>

          <div class="filter-section">
            <h3 class="filter-title">
              <i class="fas fa-book"></i>
              Disciplinas
            </h3>
            <div class="filter-chips scrollable">
              <button 
                v-for="subject in subjects" 
                :key="subject.id"
                @click="toggleFilter('subject', subject.id)"
                :class="{ active: filters.subject.includes(subject.id) }"
                class="filter-chip subject-chip"
                :style="{ '--subject-color': subject.color }"
              >
                <span class="subject-dot"></span>
                {{ subject.name }}
              </button>
            </div>
          </div>

          <div class="filter-actions">
            <button @click="clearFilters" class="clear-filters-button">
              <i class="fas fa-times"></i>
              Limpar Filtros
            </button>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Calendar Content -->
    <div class="calendar-content">
      <!-- Month View -->
      <transition name="view-transition">
        <div v-if="currentView === 'month'" class="month-view">
          <div class="weekdays">
            <div v-for="day in weekDays" :key="day" class="weekday">{{ day }}</div>
          </div>
          <div class="month-grid">
            <div 
              v-for="(day, index) in calendarDays" 
              :key="index"
              :class="getDayClasses(day)"
              @click="selectDay(day.date)"
              class="calendar-day"
            >
              <div class="day-header">
                <span class="day-number">{{ day.day }}</span>
                <span v-if="isToday(day.date)" class="today-badge">Hoje</span>
              </div>
              <div class="day-events">
                <div 
                  v-for="event in day.events.slice(0, 3)" 
                  :key="event.id"
                  @click.stop="selectEvent(event)"
                  class="event-preview"
                  :style="{ borderColor: getEventColor(event) }"
                >
                  <span class="event-time">{{ formatEventTime(event.start) }}</span>
                  <span class="event-title">
                    <span v-if="event.difficulty" class="difficulty-indicator" :data-difficulty="event.difficulty">
                      <span class="difficulty-bars">
                        <span class="bar"></span>
                        <span class="bar"></span>
                        <span class="bar"></span>
                      </span>
                    </span>
                    {{ getCleanTitle(event.title) }}
                  </span>
                </div>
                <button 
                  v-if="day.events.length > 3" 
                  class="more-events"
                  @click.stop="showDayEvents(day)"
                >
                  +{{ day.events.length - 3 }} mais
                </button>
              </div>
            </div>
          </div>
        </div>
      </transition>

      <!-- Week View -->
      <transition name="view-transition">
        <div v-if="currentView === 'week'" class="week-view">
          <div class="week-header">
            <div class="time-column"></div>
            <div 
              v-for="day in weekViewDays" 
              :key="day.date"
              class="week-day-header"
              :class="{ today: isToday(day.date) }"
            >
              <div class="week-day-label">{{ day.dayName }}</div>
              <div class="week-day-number">{{ day.dayNumber }}</div>
            </div>
          </div>
          <div class="week-content">
            <div class="time-slots">
              <div v-for="hour in 24" :key="hour" class="time-slot">
                <div class="time-label">{{ formatHour(hour - 1) }}</div>
                <div class="time-grid">
                  <div 
                    v-for="day in weekViewDays" 
                    :key="`${day.date}-${hour}`"
                    class="time-cell"
                    @click="createEventAt(day.date, hour - 1)"
                  >
                    <div 
                      v-for="event in getEventsForHour(day.date, hour - 1)" 
                      :key="event.id"
                      class="week-event"
                      :style="getEventStyle(event)"
                      @click.stop="selectEvent(event)"
                    >
                      <div class="week-event-content">
                        <span class="week-event-time">{{ formatEventTime(event.start) }}</span>
                        <span class="week-event-title">{{ event.title }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>

      <!-- Day View -->
      <transition name="view-transition">
        <div v-if="currentView === 'day'" class="day-view">
          <div class="day-view-header">
            <h2 class="day-view-title">{{ formatDate(selectedDay, 'EEEE, dd \'de\' MMMM', { locale: ptBR }) }}</h2>
            <div class="day-stats">
              <div class="stat-card">
                <i class="fas fa-calendar-check"></i>
                <span class="stat-value">{{ dayEvents.length }}</span>
                <span class="stat-label">Revisões</span>
              </div>
              <div class="stat-card">
                <i class="fas fa-clock"></i>
                <span class="stat-value">{{ totalStudyTime }}</span>
                <span class="stat-label">Tempo Total</span>
              </div>
            </div>
          </div>
          
          <div class="day-timeline">
            <div v-for="hour in 24" :key="hour" class="hour-block">
              <div class="hour-label">{{ formatHour(hour - 1) }}</div>
              <div class="hour-content">
                <div 
                  v-for="event in getEventsForHour(selectedDay, hour - 1)" 
                  :key="event.id"
                  class="timeline-event"
                  :style="{ backgroundColor: getEventColor(event) + '20', borderColor: getEventColor(event) }"
                  @click="selectEvent(event)"
                >
                  <div class="timeline-event-header">
                    <i :class="getRevisionIcon(event.type)"></i>
                    <span class="timeline-event-time">{{ formatEventTime(event.start) }} - {{ formatEventTime(event.end) }}</span>
                  </div>
                  <h4 class="timeline-event-title">{{ event.title }}</h4>
                  <div class="timeline-event-meta">
                    <span class="subject-tag" :style="{ color: getSubjectColor(event.subject) }">
                      {{ getSubjectName(event.subject) }}
                    </span>
                    <span v-if="event.progress" class="progress-indicator">
                      <i class="fas fa-chart-line"></i>
                      {{ event.progress }}%
                    </span>
                  </div>
                </div>
                <div 
                  v-if="getEventsForHour(selectedDay, hour - 1).length === 0" 
                  class="empty-hour"
                  @click="createEventAt(selectedDay, hour - 1)"
                >
                  <i class="fas fa-plus"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>

      <!-- List View -->
      <transition name="view-transition">
        <div v-if="currentView === 'list'" class="list-view">
          <!-- Modern Search and Filter Bar -->
          <div class="list-header">
            <div class="list-header-content">
              <div class="search-section">
                <div class="search-wrapper">
                  <div class="search-icon-wrapper">
                    <i class="fas fa-search"></i>
                  </div>
                  <input 
                    v-model="searchQuery" 
                    type="text" 
                    placeholder="Buscar por título, disciplina ou descrição..."
                    class="modern-search-input"
                  >
                  <transition name="fade">
                    <button 
                      v-if="searchQuery" 
                      @click="searchQuery = ''" 
                      class="clear-search"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </transition>
                </div>
              </div>
              
              <div class="control-section">
                <div class="sort-wrapper">
                  <i class="fas fa-sort-amount-down"></i>
                  <select v-model="sortBy" class="modern-sort-select">
                    <option value="date">Data</option>
                    <option value="priority">Prioridade</option>
                    <option value="subject">Disciplina</option>
                    <option value="progress">Progresso</option>
                  </select>
                </div>
                
                <div class="view-options">
                  <button 
                    class="view-option-btn"
                    :class="{ active: listViewMode === 'cards' }"
                    @click="listViewMode = 'cards'"
                    title="Visualização em Cards"
                  >
                    <i class="fas fa-th-large"></i>
                  </button>
                  <button 
                    class="view-option-btn"
                    :class="{ active: listViewMode === 'compact' }"
                    @click="listViewMode = 'compact'"
                    title="Visualização Compacta"
                  >
                    <i class="fas fa-list"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Events Timeline -->
          <div class="events-timeline">
            <!-- Stats Summary -->
            <div class="timeline-stats">
              <div class="stat-item">
                <div class="stat-icon total">
                  <i class="fas fa-layer-group"></i>
                  <div class="stat-icon-bg"></div>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ filteredEvents.length }}</span>
                  <span class="stat-label">Total de Revisões</span>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon pending">
                  <i class="fas fa-clock"></i>
                  <div class="stat-icon-bg"></div>
                  <div class="stat-icon-pulse"></div>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ pendingCount }}</span>
                  <span class="stat-label">Pendentes</span>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon completed">
                  <i class="fas fa-check-double"></i>
                  <div class="stat-icon-bg"></div>
                  <div class="stat-icon-shine"></div>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ completedCount }}</span>
                  <span class="stat-label">Concluídas</span>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon overdue">
                  <i class="fas fa-fire-alt"></i>
                  <div class="stat-icon-bg"></div>
                  <div class="stat-icon-alert"></div>
                </div>
                <div class="stat-content">
                  <span class="stat-number">{{ overdueCount }}</span>
                  <span class="stat-label">Atrasadas</span>
                </div>
              </div>
            </div>

            <!-- Event Groups -->
            <div class="event-groups">
              <transition-group name="list-transition">
                <div 
                  v-for="(group, date) in groupedEvents" 
                  :key="date" 
                  class="event-date-group"
                >
                  <!-- Group Header -->
                  <div class="group-timeline-header">
                    <div class="timeline-dot"></div>
                    <div class="timeline-content">
                      <h3 class="timeline-date">{{ formatGroupDate(date) }}</h3>
                      <div class="timeline-meta">
                        <span class="event-count">{{ group.length }} {{ group.length === 1 ? 'revisão' : 'revisões' }}</span>
                        <span class="separator">•</span>
                        <span class="time-range">{{ getGroupTimeRange(group) }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Events Container -->
                  <div class="group-events-container" :class="{ 'compact-view': listViewMode === 'compact' }">
                    <transition-group name="event-transition">
                      <div 
                        v-for="event in group" 
                        :key="event.id"
                        class="modern-event-card"
                        :class="{ 
                          'is-overdue': isOverdue(event.start),
                          'is-completed': event.completed,
                          'compact': listViewMode === 'compact'
                        }"
                        @click="selectEvent(event)"
                      >
                        <!-- Event Status Indicator -->
                        <div class="event-status-bar" :style="{ backgroundColor: getEventColor(event) }">
                          <div class="status-glow"></div>
                          <div class="status-particles">
                            <span></span>
                            <span></span>
                            <span></span>
                          </div>
                        </div>
                        
                        <!-- Event Content -->
                        <div class="event-content-wrapper">
                          <!-- Event Header -->
                          <div class="event-modern-header">
                            <div class="event-title-section">
                              <div class="event-icon-wrapper" :style="{ backgroundColor: getEventColor(event) + '20' }" :data-event-type="event.type">
                                <i :class="getRevisionIcon(event.type)"></i>
                                <div class="icon-effect-layer"></div>
                              </div>
                              <div class="event-title-info">
                                <h4 class="event-modern-title">
                                  <span v-if="event.difficulty" class="difficulty-badge-modern" :data-level="event.difficulty">
                                    <span class="difficulty-icon">
                                      <i v-if="event.difficulty === 'easy'" class="fas fa-battery-quarter"></i>
                                      <i v-else-if="event.difficulty === 'medium'" class="fas fa-battery-half"></i>
                                      <i v-else class="fas fa-battery-full"></i>
                                    </span>
                                    <span class="difficulty-glow"></span>
                                  </span>
                                  {{ getCleanTitle(event.title) }}
                                </h4>
                                <div class="event-meta-info">
                                  <span class="meta-item time">
                                    <i class="far fa-clock"></i>
                                    {{ formatEventTime(event.start) }} - {{ formatEventTime(event.end) }}
                                  </span>
                                  <span class="meta-item duration">
                                    <i class="fas fa-hourglass-half"></i>
                                    {{ getEventDuration(event) }}
                                  </span>
                                </div>
                              </div>
                            </div>
                            
                            <div class="event-priority-section">
                              <span 
                                class="modern-priority-badge" 
                                :data-priority="event.priority?.toLowerCase()"
                              >
                                <span class="priority-indicator">
                                  <i v-if="event.priority === 'Alta'" class="fas fa-fire"></i>
                                  <i v-else-if="event.priority === 'Média'" class="fas fa-bolt"></i>
                                  <i v-else class="fas fa-leaf"></i>
                                  <span class="priority-pulse"></span>
                                </span>
                                {{ event.priority }}
                              </span>
                            </div>
                          </div>
                          
                          <!-- Event Body -->
                          <div class="event-modern-body">
                            <div class="event-info-grid">
                              <div class="info-item">
                                <span class="type-icon-wrapper" :data-type="event.type">
                                  <i :class="getRevisionIcon(event.type)"></i>
                                  <span class="type-effect"></span>
                                </span>
                                <span>{{ event.type }}</span>
                              </div>
                              <div class="info-item">
                                <i class="fas fa-book"></i>
                                <span :style="{ color: getSubjectColor(event.subject) }">{{ getSubjectName(event.subject) }}</span>
                              </div>
                              <div v-if="event.location" class="info-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ event.location }}</span>
                              </div>
                            </div>
                            
                            <!-- Progress Section -->
                            <div v-if="event.progress !== undefined" class="event-progress-section">
                              <div class="progress-header">
                                <span class="progress-label">Progresso</span>
                                <span class="progress-value">{{ event.progress }}%</span>
                              </div>
                              <div class="modern-progress-bar">
                                <div 
                                  class="progress-track"
                                >
                                  <div 
                                    class="progress-fill-modern" 
                                    :style="{ 
                                      width: event.progress + '%', 
                                      backgroundColor: getProgressColor(event.progress) 
                                    }"
                                  >
                                    <div class="progress-glow"></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <!-- Event Footer -->
                          <div class="event-modern-footer">
                            <div class="event-tags">
                              <span v-if="isToday(event.start)" class="event-tag today">
                                <span class="tag-icon-wrapper">
                                  <i class="fas fa-sun"></i>
                                  <span class="tag-sparkle"></span>
                                </span>
                                Hoje
                              </span>
                              <span v-if="isOverdue(event.start)" class="event-tag overdue">
                                <span class="tag-icon-wrapper">
                                  <i class="fas fa-hourglass-end"></i>
                                  <span class="tag-pulse"></span>
                                </span>
                                Atrasado
                              </span>
                              <span v-if="event.completed" class="event-tag completed">
                                <span class="tag-icon-wrapper">
                                  <i class="fas fa-trophy"></i>
                                  <span class="tag-glow"></span>
                                </span>
                                Concluído
                              </span>
                            </div>
                            
                            <div class="event-modern-actions">
                              <button 
                                @click.stop="startRevision(event)" 
                                class="modern-action-btn primary"
                                title="Iniciar Revisão"
                              >
                                <i class="fas fa-play"></i>
                                <span>Iniciar</span>
                              </button>
                              <button 
                                @click.stop="editEvent(event)" 
                                class="modern-action-btn"
                                title="Editar"
                              >
                                <i class="fas fa-edit"></i>
                              </button>
                              <button 
                                @click.stop="deleteEvent(event)" 
                                class="modern-action-btn danger"
                                title="Excluir"
                              >
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </transition-group>
                  </div>
                </div>
              </transition-group>
              
              <!-- Empty State -->
              <transition name="fade">
                <div v-if="Object.keys(groupedEvents).length === 0" class="modern-empty-state">
                  <div class="empty-illustration">
                    <div class="empty-calendar-icon">
                      <i class="far fa-calendar-times"></i>
                      <div class="floating-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                  <h3 class="empty-title">Nenhuma revisão encontrada</h3>
                  <p class="empty-description">
                    {{ searchQuery ? 'Tente ajustar seus filtros de busca' : 'Comece criando sua primeira revisão para organizar seus estudos' }}
                  </p>
                  <button @click="handleNewRevision" class="create-revision-btn">
                    <div class="btn-icon">
                      <i class="fas fa-plus"></i>
                    </div>
                    <span>Criar Nova Revisão</span>
                  </button>
                </div>
              </transition>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- Modals -->
    <transition name="modal-fade">
      <div v-if="showRevisionForm" class="modal-overlay" @click.self="showRevisionForm = false">
        <div class="modal-container">
          <button class="modal-close" @click="showRevisionForm = false">
            <i class="fas fa-times"></i>
          </button>
          <MiniRevisionScheduler 
            @close="showRevisionForm = false"
            @revision-scheduled="handleRevisionScheduled"
          />
        </div>
      </div>
    </transition>

    <SubjectManager
      v-if="showSubjectModal"
      @close="showSubjectModal = false"
      @update="loadSubjects"
    />

    <EventDetail
      v-if="selectedEvent"
      :event="selectedEvent"
      :subjects="subjects"
      @close="selectedEvent = null"
      @edit="editEvent"
      @delete="deleteEvent"
    />
  </div>
</template>

<script>
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addDays, subDays, 
         differenceInDays, isSameDay, isSameMonth, parseISO, addMonths, subMonths,
         addWeeks, subWeeks, getDay, getDaysInMonth, startOfDay, endOfDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import MiniRevisionScheduler from './MiniRevisionScheduler.vue';
import SubjectManager from './SubjectManager.vue';
import EventDetail from './EventDetail.vue';

export default {
  name: 'CalendarView',
  components: {
    MiniRevisionScheduler,
    SubjectManager,
    EventDetail
  },
  data() {
    return {
      currentView: 'month',
      currentDate: new Date(),
      selectedDay: new Date(),
      showFilters: false,
      showRevisionForm: false,
      showSubjectModal: false,
      selectedEvent: null,
      searchQuery: '',
      sortBy: 'date',
      listViewMode: 'cards',
      
      viewOptions: [
        { id: 'month', label: 'Mês', icon: 'fas fa-calendar-alt' },
        { id: 'week', label: 'Semana', icon: 'fas fa-calendar-week' },
        { id: 'day', label: 'Dia', icon: 'fas fa-calendar-day' },
        { id: 'list', label: 'Lista', icon: 'fas fa-list' }
      ],
      
      filters: {
        type: [],
        priority: [],
        subject: []
      },
      
      revisionTypes: ['Revisão', 'Prova', 'Trabalho', 'Apresentação', 'Exercício'],
      priorities: ['Alta', 'Média', 'Baixa'],
      
      weekDays: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
    };
  },
  
  computed: {
    subjects() {
      return this.$store.state.subjects?.subjects || [];
    },
    
    events() {
      return this.$store.state.calendar?.events || [];
    },
    
    filteredEvents() {
      let events = this.events.map(event => {
        // Adiciona a propriedade difficulty baseada no título
        return {
          ...event,
          difficulty: this.extractDifficulty(event.title)
        };
      });
      
      // Apply filters
      if (this.filters.type.length > 0) {
        events = events.filter(e => this.filters.type.includes(e.type));
      }
      if (this.filters.priority.length > 0) {
        events = events.filter(e => this.filters.priority.includes(e.priority));
      }
      if (this.filters.subject.length > 0) {
        events = events.filter(e => this.filters.subject.includes(e.subject));
      }
      
      // Apply search
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        events = events.filter(e => 
          e.title.toLowerCase().includes(query) ||
          e.description?.toLowerCase().includes(query) ||
          this.getSubjectName(e.subject).toLowerCase().includes(query)
        );
      }
      
      return events;
    },
    
    activeFiltersCount() {
      return this.filters.type.length + this.filters.priority.length + this.filters.subject.length;
    },
    
    formattedCurrentPeriod() {
      if (this.currentView === 'month') {
        return format(this.currentDate, 'MMMM yyyy', { locale: ptBR });
      } else if (this.currentView === 'week') {
        const start = startOfWeek(this.currentDate);
        const end = endOfWeek(this.currentDate);
        return `${format(start, 'dd MMM')} - ${format(end, 'dd MMM yyyy')}`;
      } else if (this.currentView === 'day') {
        return format(this.selectedDay, 'dd \'de\' MMMM \'de\' yyyy', { locale: ptBR });
      }
      return '';
    },
    
    calendarDays() {
      const start = startOfMonth(this.currentDate);
      const end = endOfMonth(this.currentDate);
      const startDate = startOfWeek(start);
      const endDate = endOfWeek(end);
      
      const days = [];
      let date = startDate;
      
      while (date <= endDate) {
        const dayEvents = this.getEventsForDay(date);
        days.push({
          date: new Date(date),
          day: format(date, 'd'),
          isCurrentMonth: isSameMonth(date, this.currentDate),
          isToday: isSameDay(date, new Date()),
          isSelected: isSameDay(date, this.selectedDay),
          events: dayEvents
        });
        date = addDays(date, 1);
      }
      
      return days;
    },
    
    weekViewDays() {
      const start = startOfWeek(this.currentDate);
      const days = [];
      
      for (let i = 0; i < 7; i++) {
        const date = addDays(start, i);
        days.push({
          date: date,
          dayName: format(date, 'EEE', { locale: ptBR }),
          dayNumber: format(date, 'd')
        });
      }
      
      return days;
    },
    
    dayEvents() {
      return this.getEventsForDay(this.selectedDay);
    },
    
    totalStudyTime() {
      const totalMinutes = this.dayEvents.reduce((total, event) => {
        const start = new Date(event.start);
        const end = new Date(event.end);
        return total + (end - start) / (1000 * 60);
      }, 0);
      
      const hours = Math.floor(totalMinutes / 60);
      const minutes = Math.round(totalMinutes % 60);
      
      if (hours > 0) {
        return `${hours}h ${minutes}min`;
      }
      return `${minutes}min`;
    },
    
    groupedEvents() {
      const groups = {};
      
      const sortedEvents = [...this.filteredEvents].sort((a, b) => {
        if (this.sortBy === 'date') {
          return new Date(a.start) - new Date(b.start);
        } else if (this.sortBy === 'priority') {
          const priorityOrder = { 'Alta': 0, 'Média': 1, 'Baixa': 2 };
          return priorityOrder[a.priority] - priorityOrder[b.priority];
        } else if (this.sortBy === 'subject') {
          return this.getSubjectName(a.subject).localeCompare(this.getSubjectName(b.subject));
        } else if (this.sortBy === 'progress') {
          return (b.progress || 0) - (a.progress || 0);
        }
        return 0;
      });
      
      sortedEvents.forEach(event => {
        const date = format(new Date(event.start), 'yyyy-MM-dd');
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(event);
      });
      
      return groups;
    },
    
    tabIndicatorStyle() {
      const index = this.viewOptions.findIndex(v => v.id === this.currentView);
      return {
        transform: `translateX(${index * 100}%)`
      };
    },
    
    pendingCount() {
      const now = new Date();
      return this.filteredEvents.filter(e => new Date(e.start) > now && !e.completed).length;
    },
    
    completedCount() {
      return this.filteredEvents.filter(e => e.completed).length;
    },
    
    overdueCount() {
      const now = new Date();
      return this.filteredEvents.filter(e => new Date(e.start) < now && !e.completed && !this.isToday(e.start)).length;
    }
  },
  
  methods: {
    switchView(view) {
      this.currentView = view;
      if (view === 'day' && !this.selectedDay) {
        this.selectedDay = new Date();
      }
    },
    
    previousPeriod() {
      if (this.currentView === 'month') {
        this.currentDate = subMonths(this.currentDate, 1);
      } else if (this.currentView === 'week') {
        this.currentDate = subWeeks(this.currentDate, 1);
      } else if (this.currentView === 'day') {
        this.selectedDay = subDays(this.selectedDay, 1);
      }
      this.fetchEvents();
    },
    
    nextPeriod() {
      if (this.currentView === 'month') {
        this.currentDate = addMonths(this.currentDate, 1);
      } else if (this.currentView === 'week') {
        this.currentDate = addWeeks(this.currentDate, 1);
      } else if (this.currentView === 'day') {
        this.selectedDay = addDays(this.selectedDay, 1);
      }
      this.fetchEvents();
    },
    
    goToToday() {
      const today = new Date();
      this.currentDate = today;
      this.selectedDay = today;
      if (this.currentView === 'day') {
        this.currentView = 'day';
      }
      this.fetchEvents();
    },
    
    toggleFilters() {
      this.showFilters = !this.showFilters;
    },
    
    toggleFilter(type, value) {
      const index = this.filters[type].indexOf(value);
      if (index > -1) {
        this.filters[type].splice(index, 1);
      } else {
        this.filters[type].push(value);
      }
    },
    
    clearFilters() {
      this.filters = {
        type: [],
        priority: [],
        subject: []
      };
    },
    
    selectDay(date) {
      this.selectedDay = date;
      if (this.currentView === 'month') {
        this.currentView = 'day';
      }
    },
    
    selectEvent(event) {
      this.selectedEvent = event;
    },
    
    editEvent(event) {
      // Implement edit functionality
      console.log('Edit event:', event);
    },
    
    deleteEvent(event) {
      // Implement delete functionality
      console.log('Delete event:', event);
    },
    
    createEventAt(date, hour) {
      this.selectedDay = date;
      this.showRevisionForm = true;
      // Pass the hour to the form
    },
    
    saveRevision(revision) {
      console.log('Salvando revisão:', revision);
      this.$store.dispatch('calendar/addEvent', revision)
        .then(() => {
          console.log('Revisão salva com sucesso');
          this.showRevisionForm = false;
          this.fetchEvents();
        })
        .catch(error => {
          console.error('Erro ao salvar revisão:', error);
          alert('Erro ao salvar revisão. Por favor, tente novamente.');
        });
    },
    
    getEventsForDay(date) {
      const dayStart = startOfDay(date);
      const dayEnd = endOfDay(date);
      
      return this.filteredEvents.filter(event => {
        const eventDate = new Date(event.start);
        return eventDate >= dayStart && eventDate <= dayEnd;
      });
    },
    
    getEventsForHour(date, hour) {
      const dayEvents = this.getEventsForDay(date);
      return dayEvents.filter(event => {
        const eventHour = new Date(event.start).getHours();
        return eventHour === hour;
      });
    },
    
    getDayClasses(day) {
      return {
        'other-month': !day.isCurrentMonth,
        'today': day.isToday,
        'selected': day.isSelected,
        'has-events': day.events.length > 0
      };
    },
    
    getRevisionIcon(type) {
      const icons = {
        'Revisão': 'fas fa-book-reader',
        'Prova': 'fas fa-file-alt',
        'Trabalho': 'fas fa-briefcase',
        'Apresentação': 'fas fa-presentation',
        'Exercício': 'fas fa-pencil-alt'
      };
      return icons[type] || 'fas fa-calendar';
    },
    
    getEventColor(event) {
      if (event.subject) {
        return this.getSubjectColor(event.subject);
      }
      const colors = {
        'Alta': '#ff4757',
        'Média': '#ffa502',
        'Baixa': '#2ed573'
      };
      return colors[event.priority] || '#667eea';
    },
    
    getEventStyle(event) {
      const color = this.getEventColor(event);
      return {
        backgroundColor: color + '20',
        borderLeft: `3px solid ${color}`,
        color: color
      };
    },
    
    formatEventTime(dateStr) {
      return format(new Date(dateStr), 'HH:mm');
    },
    
    formatHour(hour) {
      return `${hour.toString().padStart(2, '0')}:00`;
    },
    
    formatDate(date, formatStr, options) {
      return format(date, formatStr, options);
    },
    
    formatGroupDate(dateStr) {
      const date = new Date(dateStr);
      const today = new Date();
      
      if (isSameDay(date, today)) {
        return 'Hoje';
      } else if (isSameDay(date, addDays(today, 1))) {
        return 'Amanhã';
      } else if (isSameDay(date, subDays(today, 1))) {
        return 'Ontem';
      }
      
      return format(date, "EEEE, dd 'de' MMMM", { locale: ptBR });
    },
    
    getSubjectName(subjectId) {
      const subject = this.subjects.find(s => s.id === subjectId);
      return subject ? subject.name : 'Sem disciplina';
    },
    
    getSubjectColor(subjectId) {
      const subject = this.subjects.find(s => s.id === subjectId);
      return subject ? subject.color : '#667eea';
    },
    
    getProgressColor(progress) {
      if (progress >= 80) return '#2ed573';
      if (progress >= 60) return '#3742fa';
      if (progress >= 40) return '#ffa502';
      return '#ff4757';
    },
    
    isToday(date) {
      return isSameDay(date, new Date());
    },
    
    isOverdue(dateStr) {
      const date = new Date(dateStr);
      return date < new Date() && !this.isToday(date);
    },
    
    showDayEvents(day) {
      this.selectedDay = day.date;
      this.currentView = 'day';
    },
    
    getEventDuration(event) {
      const start = new Date(event.start);
      const end = new Date(event.end);
      const minutes = Math.floor((end - start) / 60000);
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      if (hours > 0) {
        return `${hours}h ${mins}min`;
      }
      return `${mins}min`;
    },
    
    getGroupTimeRange(group) {
      if (group.length === 0) return '';
      const times = group.map(e => new Date(e.start).getHours());
      const minHour = Math.min(...times);
      const maxHour = Math.max(...times);
      return `${minHour.toString().padStart(2, '0')}:00 - ${maxHour.toString().padStart(2, '0')}:00`;
    },
    
    startRevision(event) {
      // Implement start revision logic
      console.log('Starting revision:', event);
      this.$router.push(`/revision/${event.id}`);
    },
    
    getCleanTitle(title) {
      // Remove os indicadores de dificuldade do título
      return title.replace(/\[[★☆▰]+\]/g, '').trim();
    },
    
    extractDifficulty(title) {
      // Extrai o nível de dificuldade do título
      if (title.includes('[▰]') || title.includes('[★☆☆]')) return 'easy';
      if (title.includes('[▰▰]') || title.includes('[★★☆]')) return 'medium';
      if (title.includes('[▰▰▰]') || title.includes('[★★★]')) return 'hard';
      return null;
    },
    
    async fetchEvents() {
      try {
        let start, end;
        
        if (this.currentView === 'month') {
          start = startOfMonth(this.currentDate);
          end = endOfMonth(this.currentDate);
        } else if (this.currentView === 'week') {
          start = startOfWeek(this.currentDate);
          end = endOfWeek(this.currentDate);
        } else {
          start = subDays(this.selectedDay, 3);
          end = addDays(this.selectedDay, 3);
        }
        
        await this.$store.dispatch('calendar/fetchEvents', {
          start: format(start, 'yyyy-MM-dd'),
          end: format(end, 'yyyy-MM-dd')
        });
      } catch (error) {
        console.error('Error fetching events:', error);
      }
    },
    
    loadSubjects() {
      console.log('Carregando disciplinas...');
      this.$store.dispatch('subjects/fetchSubjects').then(() => {
        console.log('Disciplinas carregadas:', this.subjects);
      });
    },
    
    handleNewRevision() {
      console.log('Botão Nova Revisão clicado');
      console.log('showRevisionForm antes:', this.showRevisionForm);
      this.showRevisionForm = true;
      console.log('showRevisionForm depois:', this.showRevisionForm);
    },

    handleRevisionScheduled(revisionData) {
      console.log('Revisão agendada:', revisionData);
      // Fechar o modal
      this.showRevisionForm = false;
      // Recarregar os eventos para mostrar a nova revisão
      this.fetchEvents();
      // Mostrar notificação de sucesso (opcional)
      this.$toast?.success('Revisão agendada com sucesso!');
    }
  },
  
  mounted() {
    this.selectedDay = new Date();
    this.loadSubjects();
    this.fetchEvents();
  }
};
</script>

<style scoped>
/* Modern Professional Styles */
.calendar-view {
  position: relative;
  min-height: 100vh;
  background: #0a0f1b;
  color: #e4e6eb;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

.gradient-orb.orb-1 {
  width: 600px;
  height: 600px;
  top: -200px;
  left: -200px;
  background: radial-gradient(circle, #6366f1, transparent);
}

.gradient-orb.orb-2 {
  width: 800px;
  height: 800px;
  bottom: -300px;
  right: -300px;
  background: radial-gradient(circle, #8b5cf6, transparent);
  animation-delay: -7s;
}

.gradient-orb.orb-3 {
  width: 500px;
  height: 500px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, #3b82f6, transparent);
  animation-delay: -14s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-30px, 30px) scale(0.9); }
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.05) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 10s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Header Styles */
.calendar-header {
  position: relative;
  z-index: 10;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 1.5rem 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.page-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.2);
}

.page-info h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #e4e6eb, #94a3b8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

/* Date Navigation */
.date-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(30, 41, 59, 0.5);
  padding: 0.5rem;
  border-radius: 12px;
}

.nav-button {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  color: #94a3b8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.current-period {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #e4e6eb;
  padding: 0 1rem;
  min-width: 200px;
  justify-content: center;
}

.today-button {
  padding: 0.5rem 1rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  color: #6366f1;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.today-button:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

/* View Switcher */
.view-switcher {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.view-tabs {
  display: flex;
  background: rgba(30, 41, 59, 0.5);
  padding: 0.25rem;
  border-radius: 12px;
  position: relative;
  gap: 0.25rem;
}

.view-tab {
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  color: #94a3b8;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 1;
}

.view-tab:hover {
  color: #e4e6eb;
}

.view-tab.active {
  color: white;
}

.tab-indicator {
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  width: calc(25% - 0.25rem);
  height: calc(100% - 0.5rem);
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 8px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.action-button {
  padding: 0.75rem 1.25rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  color: #e4e6eb;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  white-space: nowrap;
}

.action-button:hover {
  background: rgba(30, 41, 59, 0.8);
  transform: translateY(-1px);
}

.action-button.primary-button {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  color: white;
}

.action-button.primary-button:hover {
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
}

.action-button.active {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 999px;
  min-width: 18px;
  text-align: center;
}

/* Filters Panel */
.filters-panel {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  padding: 1.5rem 2rem;
  position: relative;
  z-index: 9;
}

.filters-content {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-chips.scrollable {
  max-height: 80px;
  overflow-y: auto;
}

.filter-chip {
  padding: 0.5rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.1);
  color: #94a3b8;
  border-radius: 999px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-chip:hover {
  background: rgba(30, 41, 59, 0.8);
  color: #e4e6eb;
}

.filter-chip.active {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.filter-chip.priority-chip[data-priority="alta"] {
  border-color: rgba(239, 68, 68, 0.3);
}

.filter-chip.priority-chip[data-priority="alta"].active {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.filter-chip.priority-chip[data-priority="média"] {
  border-color: rgba(245, 158, 11, 0.3);
}

.filter-chip.priority-chip[data-priority="média"].active {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.filter-chip.priority-chip[data-priority="baixa"] {
  border-color: rgba(34, 197, 94, 0.3);
}

.filter-chip.priority-chip[data-priority="baixa"].active {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.priority-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.subject-chip {
  position: relative;
}

.subject-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--subject-color);
}

.filter-actions {
  display: flex;
  align-items: flex-end;
}

.clear-filters-button {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.clear-filters-button:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* Calendar Content */
.calendar-content {
  position: relative;
  z-index: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Month View */
.month-view {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 2px;
  background: rgba(148, 163, 184, 0.1);
  padding: 2px;
  border-radius: 12px 12px 0 0;
}

.weekday {
  padding: 1.25rem 1rem;
  text-align: center;
  font-weight: 700;
  font-size: 0.9rem;
  color: #94a3b8;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.month-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  background: rgba(148, 163, 184, 0.1);
  padding: 4px;
  border-radius: 14px;
}

.calendar-day {
  min-height: 350px;
  padding: 2rem;
  background: rgba(15, 23, 42, 0.98);
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  border: 2px solid rgba(148, 163, 184, 0.2);
  display: flex;
  flex-direction: column;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.calendar-day:hover {
  background: rgba(30, 41, 59, 0.95);
  z-index: 1;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-color: rgba(99, 102, 241, 0.3);
}

.calendar-day.other-month {
  opacity: 0.3;
  background: rgba(15, 23, 42, 0.4);
}

.calendar-day.today {
  background: rgba(99, 102, 241, 0.15);
  border: 2px solid rgba(99, 102, 241, 0.4);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.1);
}

.calendar-day.selected {
  background: rgba(139, 92, 246, 0.15);
  border: 2px solid rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.15);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.day-number {
  font-weight: 800;
  font-size: 1.8rem;
  color: #e4e6eb;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}

.today-badge {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  font-size: 0.875rem;
  font-weight: 700;
  padding: 0.375rem 0.875rem;
  border-radius: 16px;
  text-transform: uppercase;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
  letter-spacing: 0.75px;
  animation: pulse 2s infinite;
}

.day-events {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px;
}

.event-preview {
  padding: 1.25rem 1.5rem;
  background: rgba(30, 41, 59, 0.95);
  border-left: 6px solid;
  border-radius: 12px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  gap: 1rem;
  align-items: center;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(15px);
  min-height: 60px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.event-preview:hover {
  background: rgba(30, 41, 59, 0.9);
  transform: translateX(3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.event-time {
  font-weight: 700;
  font-size: 1.05rem;
  color: #a0aec0;
  flex-shrink: 0;
  background: rgba(148, 163, 184, 0.2);
  padding: 0.6rem 0.9rem;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  letter-spacing: 0.5px;
}
.event-time::before {
  content: '';
  width: 4px;
  height: 4px;
  background: currentColor;
  border-radius: 50%;
  opacity: 0.6;
}

.event-title {
  color: #f1f5f9;
  font-weight: 600;
  font-size: 1.15rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  line-height: 1.6;
  letter-spacing: 0.3px;
}

/* Creative Difficulty Indicators */
.difficulty-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.375rem;
  border-radius: 6px;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.difficulty-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.difficulty-bars .bar {
  width: 3px;
  background: #475569;
  border-radius: 2px;
  transition: all 0.3s;
}

/* Easy - 1 bar active */
.difficulty-indicator[data-difficulty="easy"] {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.difficulty-indicator[data-difficulty="easy"] .bar:nth-child(1) {
  height: 12px;
  background: #22c55e;
  animation: bar-pulse 2s infinite;
}

.difficulty-indicator[data-difficulty="easy"] .bar:nth-child(2) {
  height: 8px;
  opacity: 0.3;
}

.difficulty-indicator[data-difficulty="easy"] .bar:nth-child(3) {
  height: 4px;
  opacity: 0.3;
}

/* Medium - 2 bars active */
.difficulty-indicator[data-difficulty="medium"] {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.difficulty-indicator[data-difficulty="medium"] .bar:nth-child(1) {
  height: 12px;
  background: #f59e0b;
  animation: bar-pulse 2s infinite;
}

.difficulty-indicator[data-difficulty="medium"] .bar:nth-child(2) {
  height: 8px;
  background: #f59e0b;
  animation: bar-pulse 2s infinite 0.5s;
}

.difficulty-indicator[data-difficulty="medium"] .bar:nth-child(3) {
  height: 4px;
  opacity: 0.3;
}

/* Hard - 3 bars active */
.difficulty-indicator[data-difficulty="hard"] {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.difficulty-indicator[data-difficulty="hard"] .bar {
  background: #ef4444;
}

.difficulty-indicator[data-difficulty="hard"] .bar:nth-child(1) {
  height: 12px;
  animation: bar-pulse 1.5s infinite;
}

.difficulty-indicator[data-difficulty="hard"] .bar:nth-child(2) {
  height: 8px;
  animation: bar-pulse 1.5s infinite 0.3s;
}

.difficulty-indicator[data-difficulty="hard"] .bar:nth-child(3) {
  height: 4px;
  animation: bar-pulse 1.5s infinite 0.6s;
}

@keyframes bar-pulse {
  0%, 100% { 
    transform: scaleY(1);
    opacity: 1;
  }
  50% { 
    transform: scaleY(1.3);
    opacity: 0.8;
  }
}

.more-events {
  padding: 0.75rem 1rem;
  background: rgba(99, 102, 241, 0.2);
  border: 1px solid rgba(99, 102, 241, 0.4);
  color: #818cf8;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  margin-top: 0.75rem;
}

.more-events:hover {
  background: rgba(99, 102, 241, 0.25);
  border-color: rgba(99, 102, 241, 0.4);
  transform: scale(1.02);
}

/* Week View */
.week-view {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.week-header {
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  background: rgba(30, 41, 59, 0.5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.time-column {
  background: rgba(15, 23, 42, 0.5);
}

.week-day-header {
  padding: 1rem;
  text-align: center;
  border-left: 1px solid rgba(148, 163, 184, 0.1);
}

.week-day-header.today {
  background: rgba(99, 102, 241, 0.1);
}

.week-day-label {
  font-size: 0.875rem;
  color: #94a3b8;
  font-weight: 500;
}

.week-day-number {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin-top: 0.25rem;
}

.week-content {
  max-height: 600px;
  overflow-y: auto;
}

.time-slots {
  display: flex;
  flex-direction: column;
}

.time-slot {
  display: grid;
  grid-template-columns: 80px 1fr;
  border-bottom: 1px solid rgba(148, 163, 184, 0.05);
}

.time-label {
  padding: 0.5rem;
  font-size: 0.75rem;
  color: #64748b;
  text-align: center;
  background: rgba(15, 23, 42, 0.5);
  border-right: 1px solid rgba(148, 163, 184, 0.1);
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.time-cell {
  min-height: 60px;
  border-left: 1px solid rgba(148, 163, 184, 0.05);
  cursor: pointer;
  position: relative;
  transition: background 0.2s;
}

.time-cell:hover {
  background: rgba(99, 102, 241, 0.05);
}

.week-event {
  position: absolute;
  left: 4px;
  right: 4px;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.75rem;
}

.week-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.week-event-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.week-event-time {
  font-weight: 600;
  opacity: 0.8;
}

.week-event-title {
  font-weight: 500;
}

/* Day View */
.day-view {
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.day-view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.day-view-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0;
}

.day-stats {
  display: flex;
  gap: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.stat-card i {
  font-size: 1.25rem;
  color: #6366f1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #e4e6eb;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
}

.day-timeline {
  display: flex;
  flex-direction: column;
  gap: 1px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.hour-block {
  display: grid;
  grid-template-columns: 100px 1fr;
  background: rgba(15, 23, 42, 0.8);
}

.hour-label {
  padding: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  background: rgba(30, 41, 59, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hour-content {
  padding: 0.5rem 1rem;
  position: relative;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.timeline-event {
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid;
}

.timeline-event:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.timeline-event-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.timeline-event-title {
  font-size: 1rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 0.5rem 0;
}

.timeline-event-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
}

.subject-tag {
  font-weight: 500;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
}

.empty-hour {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.empty-hour:hover {
  border-color: rgba(99, 102, 241, 0.3);
  background: rgba(99, 102, 241, 0.05);
  color: #6366f1;
}

/* Modern List View */
.list-view {
  position: relative;
  background: transparent;
  padding: 0;
}

/* List Header */
.list-header {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.list-header-content {
  display: flex;
  gap: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon-wrapper {
  position: absolute;
  left: 1.25rem;
  color: #64748b;
  transition: color 0.2s;
  z-index: 1;
}

.modern-search-input {
  width: 100%;
  padding: 1rem 3rem 1rem 3.5rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid transparent;
  border-radius: 12px;
  color: #e4e6eb;
  font-size: 0.95rem;
  transition: all 0.3s;
}

.modern-search-input:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.modern-search-input:focus ~ .search-icon-wrapper {
  color: #6366f1;
}

.clear-search {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s;
}

.clear-search:hover {
  color: #e4e6eb;
  background: rgba(148, 163, 184, 0.1);
}

.control-section {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.sort-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.25rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 10px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.sort-wrapper i {
  color: #6366f1;
}

.modern-sort-select {
  background: transparent;
  border: none;
  color: #e4e6eb;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding-right: 1.5rem;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='14' height='8' viewBox='0 0 14 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M1 1L7 7L13 1' stroke='%2394a3b8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right center;
}

.modern-sort-select:focus {
  outline: none;
}

.view-options {
  display: flex;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 10px;
  padding: 0.25rem;
  gap: 0.25rem;
}

.view-option-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-option-btn:hover {
  color: #e4e6eb;
  background: rgba(148, 163, 184, 0.1);
}

.view-option-btn.active {
  background: rgba(99, 102, 241, 0.2);
  color: #6366f1;
}

/* Timeline Stats */
.timeline-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  padding: 1.5rem;
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  font-size: 1.5rem;
  position: relative;
  overflow: hidden;
}

.stat-icon i {
  position: relative;
  z-index: 3;
}

.stat-icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  z-index: 1;
}

.stat-icon.total {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.stat-icon.total .stat-icon-bg {
  background: radial-gradient(circle at center, rgba(255,255,255,0.3) 0%, transparent 70%);
  animation: rotate-slow 20s linear infinite;
}

@keyframes rotate-slow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: white;
  position: relative;
}

.stat-icon-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150%;
  height: 150%;
  background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, transparent 70%);
  animation: pulse-stat 2s infinite;
  z-index: 2;
}

@keyframes pulse-stat {
  0%, 100% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
  50% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

.stat-icon.completed {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
}

.stat-icon-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
  animation: shine-stat 3s infinite;
  z-index: 2;
}

@keyframes shine-stat {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stat-icon.overdue {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.stat-icon-alert {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, transparent 30%, rgba(255,255,255,0.2) 70%);
  animation: alert-pulse 1s infinite;
  z-index: 2;
}

@keyframes alert-pulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #e4e6eb;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Event Groups */
.event-groups {
  position: relative;
}

.event-date-group {
  margin-bottom: 3rem;
  position: relative;
  padding-left: 2rem;
}

.event-date-group::before {
  content: '';
  position: absolute;
  left: 0.5rem;
  top: 2rem;
  bottom: -2rem;
  width: 2px;
  background: linear-gradient(to bottom, rgba(99, 102, 241, 0.3), transparent);
}

.event-date-group:last-child::before {
  display: none;
}

/* Timeline Header */
.group-timeline-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.timeline-dot {
  position: absolute;
  left: -1.5rem;
  width: 16px;
  height: 16px;
  background: #6366f1;
  border: 3px solid #0f1724;
  border-radius: 50%;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
  z-index: 2;
}

.timeline-content {
  flex: 1;
}

.timeline-date {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 0.5rem 0;
}

.timeline-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.event-count {
  font-weight: 500;
}

.separator {
  color: #475569;
}

.time-range {
  color: #64748b;
}

/* Events Container */
.group-events-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem;
}

.group-events-container.compact-view {
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

/* Modern Event Card */
.modern-event-card {
  position: relative;
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-event-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(99, 102, 241, 0.3);
}

.modern-event-card.is-overdue {
  border-color: rgba(239, 68, 68, 0.2);
}

.modern-event-card.is-completed {
  opacity: 0.7;
}

.modern-event-card.compact {
  border-radius: 12px;
}

.modern-event-card.compact .event-modern-body,
.modern-event-card.compact .event-modern-footer {
  display: none;
}

.modern-event-card.compact .event-content-wrapper {
  padding: 1rem;
}

/* Event Status Bar with Creative Effects */
.event-status-bar {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  overflow: visible;
  transition: width 0.3s;
}

.modern-event-card:hover .event-status-bar {
  width: 6px;
}

.status-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200%;
  height: 100px;
  background: inherit;
  filter: blur(8px);
  opacity: 0.6;
  animation: glow-breathing 3s infinite;
}

@keyframes glow-breathing {
  0%, 100% { opacity: 0.4; transform: translate(-50%, -50%) scaleX(1); }
  50% { opacity: 0.8; transform: translate(-50%, -50%) scaleX(1.5); }
}

.status-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.status-particles span {
  position: absolute;
  left: 50%;
  width: 2px;
  height: 2px;
  background: inherit;
  border-radius: 50%;
  opacity: 0;
  animation: particle-float 3s infinite;
}

.status-particles span:nth-child(1) {
  top: 20%;
  animation-delay: 0s;
}

.status-particles span:nth-child(2) {
  top: 50%;
  animation-delay: 1s;
}

.status-particles span:nth-child(3) {
  top: 80%;
  animation-delay: 2s;
}

@keyframes particle-float {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateX(10px) translateY(-20px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(20px) translateY(-40px) scale(0);
  }
}

/* Special effects for overdue events */
.modern-event-card.is-overdue .event-status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(239, 68, 68, 0.3), transparent);
  animation: warning-slide 2s infinite;
}

@keyframes warning-slide {
  0%, 100% { transform: translateY(-100%); }
  50% { transform: translateY(100%); }
}

/* Event Content */
.event-content-wrapper {
  padding: 1.5rem;
}

.event-modern-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.event-title-section {
  display: flex;
  gap: 1rem;
  flex: 1;
}

.event-icon-wrapper {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 1.25rem;
  color: #6366f1;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.event-icon-wrapper i {
  position: relative;
  z-index: 2;
  transition: transform 0.3s;
}

.modern-event-card:hover .event-icon-wrapper i {
  transform: scale(1.1) rotate(5deg);
}

.icon-effect-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
}

/* Unique effects for each event type */
.event-icon-wrapper[data-event-type="Revisão"] .icon-effect-layer::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(139, 92, 246, 0.3), transparent);
  animation: rotate-gradient 4s linear infinite;
}

.event-icon-wrapper[data-event-type="Prova"] .icon-effect-layer::before,
.event-icon-wrapper[data-event-type="Prova"] .icon-effect-layer::after {
  content: '';
  position: absolute;
  border: 2px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  animation: ripple-out 2s infinite;
}

.event-icon-wrapper[data-event-type="Prova"] .icon-effect-layer::after {
  animation-delay: 1s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.6);
  }
}

@keyframes rotate-gradient {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes ripple-out {
  0% {
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    transform: translate(-50%, -50%);
    opacity: 1;
  }
  100% {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: translate(0, 0);
    opacity: 0;
  }
}

.event-icon-wrapper[data-event-type="Trabalho"] .icon-effect-layer {
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(59, 130, 246, 0.1) 10px,
    rgba(59, 130, 246, 0.1) 20px
  );
  animation: stripe-move 20s linear infinite;
}

@keyframes stripe-move {
  0% { background-position: 0 0; }
  100% { background-position: 40px 40px; }
}

.event-icon-wrapper[data-event-type="Apresentação"] .icon-effect-layer::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(168, 85, 247, 0.3) 0%, transparent 70%);
  animation: spotlight 3s infinite;
}

@keyframes spotlight {
  0%, 100% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
}

.event-icon-wrapper[data-event-type="Exercício"] .icon-effect-layer {
  background: linear-gradient(to right, transparent 45%, rgba(34, 197, 94, 0.3) 50%, transparent 55%);
  animation: scan-line 2s infinite;
}

@keyframes scan-line {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.event-title-info {
  flex: 1;
}

.event-modern-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Modern Difficulty Badge for List View */
.difficulty-badge-modern {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  flex-shrink: 0;
}

.difficulty-icon {
  position: relative;
  z-index: 2;
  font-size: 1rem;
  transition: transform 0.3s;
}

.difficulty-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 8px;
  opacity: 0.5;
  filter: blur(8px);
}

/* Easy Level */
.difficulty-badge-modern[data-level="easy"] {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.1));
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.difficulty-badge-modern[data-level="easy"] .difficulty-icon {
  color: #22c55e;
  animation: battery-charge-easy 3s infinite;
}

.difficulty-badge-modern[data-level="easy"] .difficulty-glow {
  background: #22c55e;
  animation: glow-easy 3s infinite;
}

@keyframes battery-charge-easy {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes glow-easy {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.2); }
}

/* Medium Level */
.difficulty-badge-modern[data-level="medium"] {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.1));
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.difficulty-badge-modern[data-level="medium"] .difficulty-icon {
  color: #f59e0b;
  animation: battery-charge-medium 2s infinite;
}

.difficulty-badge-modern[data-level="medium"] .difficulty-glow {
  background: #f59e0b;
  animation: glow-medium 2s infinite;
}

.difficulty-badge-modern[data-level="medium"]::before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 6px;
  height: 6px;
  background: #f59e0b;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes battery-charge-medium {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.05) rotate(3deg); }
}

@keyframes glow-medium {
  0%, 100% { opacity: 0.4; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.3); }
}

@keyframes pulse-dot {
  0%, 100% { transform: scale(0.8); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 0.4; }
}

/* Hard Level */
.difficulty-badge-modern[data-level="hard"] {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.1));
  border: 1px solid rgba(239, 68, 68, 0.3);
  position: relative;
  overflow: visible;
}

.difficulty-badge-modern[data-level="hard"] .difficulty-icon {
  color: #ef4444;
  animation: battery-charge-hard 1.5s infinite;
}

.difficulty-badge-modern[data-level="hard"] .difficulty-glow {
  background: #ef4444;
  animation: glow-hard 1.5s infinite;
}

.difficulty-badge-modern[data-level="hard"]::before,
.difficulty-badge-modern[data-level="hard"]::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  border: 1px solid #ef4444;
  opacity: 0;
  animation: ripple-hard 2s infinite;
}

.difficulty-badge-modern[data-level="hard"]::after {
  animation-delay: 1s;
}

@keyframes battery-charge-hard {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1) rotate(-5deg); }
  75% { transform: scale(1.1) rotate(5deg); }
}

@keyframes glow-hard {
  0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.9; transform: translate(-50%, -50%) scale(1.4); }
}

@keyframes ripple-hard {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* Hover effect */
.modern-event-card:hover .difficulty-badge-modern .difficulty-icon {
  transform: scale(1.2);
}

.event-meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.meta-item i {
  font-size: 0.75rem;
  opacity: 0.7;
}

/* Priority Badge with Creative Indicators */
.modern-priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.875rem;
  border-radius: 999px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.priority-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.priority-indicator i {
  font-size: 0.875rem;
  z-index: 2;
  position: relative;
}

.priority-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.3;
}

.modern-priority-badge[data-priority="alta"] {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.modern-priority-badge[data-priority="alta"] .priority-pulse {
  animation: pulse-high 1.5s infinite;
}

.modern-priority-badge[data-priority="alta"] i {
  animation: fire-flicker 2s infinite;
}

@keyframes fire-flicker {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-3deg); }
  75% { transform: scale(0.95) rotate(3deg); }
}

@keyframes pulse-high {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.5; }
  50% { transform: translate(-50%, -50%) scale(1.3); opacity: 0; }
  100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.5; }
}

.modern-priority-badge[data-priority="média"] {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.05));
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.modern-priority-badge[data-priority="média"] .priority-pulse {
  animation: pulse-medium 2s infinite;
}

.modern-priority-badge[data-priority="média"] i {
  animation: bolt-strike 1.5s infinite;
}

@keyframes bolt-strike {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes pulse-medium {
  0% { transform: translate(-50%, -50%) scale(0.9); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
  100% { transform: translate(-50%, -50%) scale(0.9); opacity: 0.3; }
}

.modern-priority-badge[data-priority="baixa"] {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.modern-priority-badge[data-priority="baixa"] .priority-pulse {
  animation: pulse-low 3s infinite;
}

.modern-priority-badge[data-priority="baixa"] i {
  animation: leaf-sway 3s infinite ease-in-out;
}

@keyframes leaf-sway {
  0%, 100% { transform: rotate(-5deg); }
  50% { transform: rotate(5deg); }
}

@keyframes pulse-low {
  0% { transform: translate(-50%, -50%) scale(1); opacity: 0.2; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 0.2; }
}

/* Event Body */
.event-modern-body {
  margin-bottom: 1.25rem;
}

.event-info-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1.25rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
}

.info-item i {
  color: #64748b;
  font-size: 0.875rem;
}

/* Type Icon Creative Indicators */
.type-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background: rgba(99, 102, 241, 0.1);
}

.type-icon-wrapper i {
  position: relative;
  z-index: 2;
  color: #6366f1;
  font-size: 0.875rem;
}

.type-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 6px;
}

/* Revisão */
.type-icon-wrapper[data-type="Revisão"] {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(99, 102, 241, 0.15));
}

.type-icon-wrapper[data-type="Revisão"] i {
  color: #8b5cf6;
  animation: book-flip 3s infinite;
}

.type-icon-wrapper[data-type="Revisão"] .type-effect {
  background: linear-gradient(45deg, transparent 30%, rgba(139, 92, 246, 0.2) 50%, transparent 70%);
  animation: slide-shine 3s infinite;
}

@keyframes book-flip {
  0%, 100% { transform: rotateY(0deg); }
  50% { transform: rotateY(10deg); }
}

@keyframes slide-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Prova */
.type-icon-wrapper[data-type="Prova"] {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.15));
}

.type-icon-wrapper[data-type="Prova"] i {
  color: #ef4444;
  animation: alert-shake 2s infinite;
}

.type-icon-wrapper[data-type="Prova"] .type-effect::before,
.type-icon-wrapper[data-type="Prova"] .type-effect::after {
  content: '!';
  position: absolute;
  color: #ef4444;
  font-weight: bold;
  font-size: 0.5rem;
  opacity: 0;
  animation: alert-pop 2s infinite;
}

.type-icon-wrapper[data-type="Prova"] .type-effect::before {
  top: -2px;
  right: -2px;
}

.type-icon-wrapper[data-type="Prova"] .type-effect::after {
  bottom: -2px;
  left: -2px;
  animation-delay: 1s;
}

@keyframes alert-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

@keyframes alert-pop {
  0%, 100% { opacity: 0; transform: scale(0.5); }
  50% { opacity: 1; transform: scale(1); }
}

/* Trabalho */
.type-icon-wrapper[data-type="Trabalho"] {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.15));
}

.type-icon-wrapper[data-type="Trabalho"] i {
  color: #3b82f6;
  animation: briefcase-tilt 4s infinite ease-in-out;
}

.type-icon-wrapper[data-type="Trabalho"] .type-effect {
  border: 2px solid rgba(59, 130, 246, 0.2);
  animation: border-dash 8s linear infinite;
}

@keyframes briefcase-tilt {
  0%, 100% { transform: rotate(-5deg); }
  50% { transform: rotate(5deg); }
}

@keyframes border-dash {
  0% { border-style: solid; }
  50% { border-style: dashed; }
  100% { border-style: solid; }
}

/* Apresentação */
.type-icon-wrapper[data-type="Apresentação"] {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.15), rgba(147, 51, 234, 0.15));
}

.type-icon-wrapper[data-type="Apresentação"] i {
  color: #a855f7;
  animation: presentation-zoom 3s infinite;
}

.type-icon-wrapper[data-type="Apresentação"] .type-effect {
  background: radial-gradient(circle at center, transparent 30%, rgba(168, 85, 247, 0.2) 70%);
  animation: radial-pulse 3s infinite;
}

@keyframes presentation-zoom {
  0%, 100% { transform: scale(0.9); }
  50% { transform: scale(1.1); }
}

@keyframes radial-pulse {
  0%, 100% { transform: scale(0.8); opacity: 0; }
  50% { transform: scale(1.2); opacity: 0.5; }
}

/* Exercício */
.type-icon-wrapper[data-type="Exercício"] {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(22, 163, 74, 0.15));
}

.type-icon-wrapper[data-type="Exercício"] i {
  color: #22c55e;
  animation: pencil-write 2s infinite;
}

.type-icon-wrapper[data-type="Exercício"] .type-effect::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: #22c55e;
  animation: underline-draw 2s infinite;
}

@keyframes pencil-write {
  0%, 100% { transform: rotate(-10deg) translateX(0); }
  50% { transform: rotate(-10deg) translateX(2px); }
}

@keyframes underline-draw {
  0%, 100% { width: 0; }
  50% { width: 80%; }
}

/* Progress Section */
.event-progress-section {
  margin-top: 1rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progress-label {
  font-size: 0.875rem;
  color: #94a3b8;
  font-weight: 500;
}

.progress-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #e4e6eb;
}

.modern-progress-bar {
  position: relative;
  height: 8px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 999px;
  overflow: hidden;
}

.progress-track {
  height: 100%;
  position: relative;
}

.progress-fill-modern {
  height: 100%;
  border-radius: 999px;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 50px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-50px); }
  100% { transform: translateX(50px); }
}

/* Event Footer */
.event-modern-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.event-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.event-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.875rem;
  border-radius: 999px;
  font-size: 0.75rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.tag-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.tag-icon-wrapper i {
  position: relative;
  z-index: 2;
  font-size: 0.75rem;
}

.event-tag.today {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  color: #6366f1;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.event-tag.today i {
  animation: sun-rotate 4s linear infinite;
}

.tag-sparkle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
}

.tag-sparkle::before,
.tag-sparkle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 2px;
  background: #6366f1;
  opacity: 0.3;
  animation: sparkle 2s infinite;
}

.tag-sparkle::after {
  transform: rotate(90deg);
  animation-delay: 1s;
}

@keyframes sun-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes sparkle {
  0%, 100% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
  50% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
}

.event-tag.overdue {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.event-tag.overdue i {
  animation: hourglass-flip 2s infinite;
}

.tag-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: #ef4444;
  opacity: 0.3;
  animation: pulse-ring 1.5s infinite;
}

@keyframes hourglass-flip {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

@keyframes pulse-ring {
  0% { transform: scale(0.8); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0; }
  100% { transform: scale(0.8); opacity: 0.3; }
}

.event-tag.completed {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.event-tag.completed i {
  animation: trophy-bounce 2s infinite;
}

.tag-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, #22c55e 0%, transparent 70%);
  opacity: 0.2;
  animation: glow-pulse 2s infinite;
}

@keyframes trophy-bounce {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-2px) scale(1.1); }
}

@keyframes glow-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.2; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.4; }
}

.event-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Event Actions */
.event-modern-actions {
  display: flex;
  gap: 0.5rem;
}

.modern-action-btn {
  padding: 0.5rem 0.75rem;
  background: rgba(148, 163, 184, 0.1);
  border: none;
  color: #94a3b8;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.modern-action-btn:hover {
  background: rgba(148, 163, 184, 0.2);
  color: #e4e6eb;
  transform: scale(1.05);
}

.modern-action-btn.primary {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.modern-action-btn.primary:hover {
  background: rgba(99, 102, 241, 0.2);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.modern-action-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modern-action-btn.danger:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Modern Empty State */
.modern-empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.empty-illustration {
  margin-bottom: 2rem;
}

.empty-calendar-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 24px;
  font-size: 3rem;
  color: #6366f1;
}

.floating-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150%;
  height: 150%;
}

.floating-dots span {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #6366f1;
  border-radius: 50%;
  animation: float-dot 3s infinite ease-in-out;
}

.floating-dots span:nth-child(1) {
  top: 0;
  left: 50%;
  animation-delay: 0s;
}

.floating-dots span:nth-child(2) {
  top: 50%;
  right: 0;
  animation-delay: 1s;
}

.floating-dots span:nth-child(3) {
  bottom: 0;
  left: 50%;
  animation-delay: 2s;
}

@keyframes float-dot {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.empty-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #e4e6eb;
  margin: 0 0 0.75rem 0;
}

.empty-description {
  font-size: 1rem;
  color: #94a3b8;
  margin: 0 0 2rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.create-revision-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1.75rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  color: white;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.create-revision-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(99, 102, 241, 0.3);
}

.btn-icon {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Transitions */
.list-transition-enter-active,
.list-transition-leave-active,
.list-transition-move {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.list-transition-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.list-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.event-transition-enter-active,
.event-transition-leave-active {
  transition: all 0.3s ease;
}

.event-transition-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.event-transition-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Removed old list view styles - replaced with modern design */

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filters-slide-enter-active,
.filters-slide-leave-active {
  transition: all 0.3s ease;
}

.filters-slide-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.filters-slide-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.view-transition-enter-active,
.view-transition-leave-active {
  transition: all 0.3s ease;
}

.view-transition-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.view-transition-leave-to {
  opacity: 0;
  transform: scale(1.05);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Responsive */
@media (max-width: 1200px) {
  .view-switcher {
    gap: 1rem;
  }
  
  .action-button {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .action-button span {
    font-size: 0.875rem;
  }
}

@media (max-width: 768px) {
  .calendar-header {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .view-switcher {
    flex-direction: column;
    gap: 1rem;
  }
  
  .view-tabs {
    width: 100%;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .action-button {
    padding: 0.75rem 1rem;
    flex: 1;
    min-width: fit-content;
  }
  
  .action-button span {
    display: none;
  }
  
  .action-button i {
    font-size: 1.125rem;
  }
  
  .filters-content {
    grid-template-columns: 1fr;
  }
  
  .calendar-content {
    padding: 1rem;
  }
  
  .list-header-content {
    flex-direction: column;
  }
  
  .search-section {
    min-width: 100%;
  }
  
  .control-section {
    width: 100%;
    flex-direction: column;
    gap: 1rem;
  }
  
  .sort-wrapper,
  .view-options {
    width: 100%;
  }
  
  .timeline-stats {
    grid-template-columns: 1fr;
  }
  
  .group-events-container {
    grid-template-columns: 1fr;
  }
  
  .event-date-group {
    padding-left: 1rem;
  }
  
  .timeline-dot {
    left: -0.75rem;
  }
  
  /* Melhorias específicas para o calendário em mobile */
  .month-view {
    padding: 1rem;
  }
  
  .weekday {
    padding: 0.75rem 0.25rem;
    font-size: 0.75rem;
  }
  
  .calendar-day {
    min-height: 150px;
    padding: 1rem;
  }

  .day-number {
    font-size: 1.2rem;
  }

  .day-header {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
  }

  .event-preview {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .event-time {
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
  }

  .more-events {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .today-badge {
    font-size: 0.6rem;
    padding: 0.125rem 0.375rem;
  }
}

/* Adicionar scrollbar customizada para day-events */
.day-events::-webkit-scrollbar {
  width: 6px;
}

.day-events::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 2px;
}

.day-events::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

.day-events::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-container {
  position: relative;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  z-index: 10;
}

.modal-close:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  transform: scale(1.1);
}

/* Modal Transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: all 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>