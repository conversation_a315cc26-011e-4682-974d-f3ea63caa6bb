<template>
  <div class="revision-system-ultra">
    <!-- Advanced Background System -->
    <div class="ultra-background">
      <canvas ref="particleCanvas" class="particle-system"></canvas>
      <div class="neural-network-bg"></div>
      <div class="holographic-grid"></div>
      <div class="quantum-field">
        <div v-for="i in 20" :key="`quantum-${i}`" :class="`quantum-particle q-${i}`"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="ultra-container">
      <!-- Futuristic Header -->
      <header class="ultra-header">
        <div class="header-hologram">
          <div class="brand-section">
            <div class="logo-3d">
              <div class="logo-cube">
                <div class="cube-face front"><i class="fas fa-brain"></i></div>
                <div class="cube-face back"><i class="fas fa-graduation-cap"></i></div>
                <div class="cube-face right"><i class="fas fa-chart-line"></i></div>
                <div class="cube-face left"><i class="fas fa-atom"></i></div>
                <div class="cube-face top"><i class="fas fa-dna"></i></div>
                <div class="cube-face bottom"><i class="fas fa-microscope"></i></div>
              </div>
              <div class="energy-ring"></div>
            </div>
            <div class="brand-text">
              <h1 class="ultra-title">
                <span class="text-hologram">Revision System</span>
                <span class="text-ultra">ULTRA</span>
              </h1>
              <p class="tagline">
                <span class="typewriter">{{ currentTagline }}</span>
                <span class="cursor">|</span>
              </p>
            </div>
          </div>

          <!-- Real-time Stats Display -->
          <div class="stats-hologram">
            <div class="stat-orb" v-for="stat in liveStats" :key="stat.id">
              <div class="orb-container">
                <div class="orb-value">{{ stat.value }}</div>
                <div class="orb-label">{{ stat.label }}</div>
                <div class="orb-glow" :style="{ background: stat.color }"></div>
                <svg class="orb-ring">
                  <circle cx="40" cy="40" r="36" :stroke="stat.color" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation Tabs -->
        <nav class="ultra-nav">
          <div class="nav-track">
            <button 
              v-for="tab in navigationTabs" 
              :key="tab.id"
              :class="['nav-tab', { active: activeSection === tab.id }]"
              @click="activeSection = tab.id"
            >
              <span class="tab-icon">
                <i :class="tab.icon"></i>
              </span>
              <span class="tab-label">{{ tab.label }}</span>
              <span class="tab-badge" v-if="tab.badge">{{ tab.badge }}</span>
              <div class="tab-highlight"></div>
            </button>
            <div class="nav-indicator" :style="navIndicatorStyle"></div>
          </div>
        </nav>
      </header>

      <!-- Dashboard Section -->
      <section v-if="activeSection === 'dashboard'" class="section-dashboard">
        <div class="dashboard-grid">
          <!-- Performance Analytics -->
          <div class="analytics-card glass-ultra">
            <h3 class="card-title">
              <i class="fas fa-analytics"></i>
              Performance Analytics
            </h3>
            <div class="chart-container">
              <canvas ref="performanceChart"></canvas>
              <div class="chart-overlay">
                <div class="performance-score">
                  <span class="score-value">{{ overallScore }}</span>
                  <span class="score-label">Overall Score</span>
                </div>
              </div>
            </div>
            <div class="metrics-grid">
              <div class="metric" v-for="metric in performanceMetrics" :key="metric.id">
                <div class="metric-icon" :style="{ color: metric.color }">
                  <i :class="metric.icon"></i>
                </div>
                <div class="metric-data">
                  <span class="metric-value">{{ metric.value }}</span>
                  <span class="metric-label">{{ metric.label }}</span>
                </div>
                <div class="metric-trend" :class="metric.trend">
                  <i :class="metric.trend === 'up' ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                  {{ metric.change }}%
                </div>
              </div>
            </div>
          </div>

          <!-- Heatmap Calendar -->
          <div class="heatmap-card glass-ultra">
            <h3 class="card-title">
              <i class="fas fa-fire"></i>
              Study Heatmap
            </h3>
            <div class="heatmap-container">
              <div class="heatmap-months">
                <span v-for="month in months" :key="month">{{ month }}</span>
              </div>
              <div class="heatmap-grid">
                <div class="heatmap-week" v-for="week in 52" :key="`week-${week}`">
                  <div 
                    v-for="day in 7" 
                    :key="`day-${day}`"
                    class="heatmap-cell"
                    :data-intensity="getHeatmapIntensity(week, day)"
                    @mouseover="showDayTooltip($event, week, day)"
                    @mouseleave="hideDayTooltip"
                  >
                    <div class="cell-glow"></div>
                  </div>
                </div>
              </div>
              <div class="heatmap-legend">
                <span>Less</span>
                <div class="legend-cells">
                  <div class="heatmap-cell" data-intensity="0"></div>
                  <div class="heatmap-cell" data-intensity="1"></div>
                  <div class="heatmap-cell" data-intensity="2"></div>
                  <div class="heatmap-cell" data-intensity="3"></div>
                  <div class="heatmap-cell" data-intensity="4"></div>
                </div>
                <span>More</span>
              </div>
            </div>
          </div>

          <!-- AI Insights -->
          <div class="insights-card glass-ultra">
            <h3 class="card-title">
              <i class="fas fa-robot"></i>
              AI Insights
            </h3>
            <div class="insights-list">
              <div 
                v-for="insight in aiInsights" 
                :key="insight.id"
                class="insight-item"
                :class="`insight-${insight.type}`"
              >
                <div class="insight-icon">
                  <i :class="insight.icon"></i>
                  <div class="icon-pulse"></div>
                </div>
                <div class="insight-content">
                  <h4>{{ insight.title }}</h4>
                  <p>{{ insight.description }}</p>
                  <div class="insight-action" v-if="insight.action">
                    <button @click="executeInsightAction(insight)">
                      {{ insight.action.label }}
                      <i class="fas fa-arrow-right"></i>
                    </button>
                  </div>
                </div>
                <div class="insight-priority">
                  <span :class="`priority-${insight.priority}`">
                    {{ insight.priority }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Progress Visualization -->
          <div class="progress-card glass-ultra">
            <h3 class="card-title">
              <i class="fas fa-trophy"></i>
              Achievement Progress
            </h3>
            <div class="achievements-grid">
              <div 
                v-for="achievement in achievements" 
                :key="achievement.id"
                class="achievement-item"
                :class="{ unlocked: achievement.unlocked }"
              >
                <div class="achievement-icon">
                  <i :class="achievement.icon"></i>
                  <svg class="progress-ring">
                    <circle 
                      cx="30" 
                      cy="30" 
                      r="28"
                      :stroke-dasharray="`${achievement.progress * 1.76} 176`"
                    />
                  </svg>
                </div>
                <div class="achievement-info">
                  <h5>{{ achievement.name }}</h5>
                  <p>{{ achievement.description }}</p>
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: achievement.progress + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Scheduler Section -->
      <section v-if="activeSection === 'scheduler'" class="section-scheduler">
        <div class="scheduler-container">
          <!-- Advanced Scheduler Form -->
          <div class="scheduler-panel glass-ultra">
            <h3 class="panel-title">
              <i class="fas fa-calendar-plus"></i>
              Intelligent Scheduler
            </h3>
            
            <form @submit.prevent="scheduleRevisions" class="ultra-form">
              <!-- Subject Selection with AI Suggestions -->
              <div class="form-section">
                <label class="form-label">
                  <i class="fas fa-book"></i>
                  Subject / Topic
                </label>
                <div class="input-group">
                  <input 
                    v-model="schedulerForm.subject" 
                    type="text" 
                    class="ultra-input"
                    placeholder="Enter subject or topic..."
                    @input="getSuggestions"
                  />
                  <button type="button" class="ai-suggest-btn" @click="useAISuggestion">
                    <i class="fas fa-magic"></i>
                    AI Suggest
                  </button>
                </div>
                <div v-if="subjectSuggestions.length" class="suggestions-dropdown">
                  <div 
                    v-for="suggestion in subjectSuggestions" 
                    :key="suggestion"
                    class="suggestion-item"
                    @click="schedulerForm.subject = suggestion"
                  >
                    {{ suggestion }}
                  </div>
                </div>
              </div>

              <!-- Difficulty Assessment -->
              <div class="form-section">
                <label class="form-label">
                  <i class="fas fa-tachometer-alt"></i>
                  Difficulty Assessment
                </label>
                <div class="difficulty-matrix">
                  <div 
                    v-for="level in difficultyLevels" 
                    :key="level.id"
                    class="difficulty-cell"
                    :class="{ selected: schedulerForm.difficulty === level.id }"
                    @click="schedulerForm.difficulty = level.id"
                  >
                    <div class="cell-icon">
                      <i :class="level.icon"></i>
                    </div>
                    <div class="cell-info">
                      <h5>{{ level.name }}</h5>
                      <p>{{ level.description }}</p>
                      <div class="interval-preview">
                        <span v-for="(interval, idx) in level.intervals" :key="idx">
                          {{ interval }}d
                        </span>
                      </div>
                    </div>
                    <div class="cell-glow" :style="{ background: level.color }"></div>
                  </div>
                </div>
              </div>

              <!-- Learning Style Optimization -->
              <div class="form-section">
                <label class="form-label">
                  <i class="fas fa-brain"></i>
                  Learning Style
                </label>
                <div class="style-selector">
                  <div 
                    v-for="style in learningStyles" 
                    :key="style.id"
                    class="style-option"
                    :class="{ active: schedulerForm.style === style.id }"
                    @click="schedulerForm.style = style.id"
                  >
                    <i :class="style.icon"></i>
                    <span>{{ style.name }}</span>
                  </div>
                </div>
              </div>

              <!-- Time Preferences -->
              <div class="form-section">
                <label class="form-label">
                  <i class="fas fa-clock"></i>
                  Time Preferences
                </label>
                <div class="time-grid">
                  <div class="time-input">
                    <label>Start Date</label>
                    <input 
                      v-model="schedulerForm.startDate" 
                      type="date" 
                      class="ultra-input"
                      :min="today"
                    />
                  </div>
                  <div class="time-input">
                    <label>Preferred Time</label>
                    <input 
                      v-model="schedulerForm.preferredTime" 
                      type="time" 
                      class="ultra-input"
                    />
                  </div>
                  <div class="time-input">
                    <label>Session Duration</label>
                    <select v-model="schedulerForm.duration" class="ultra-select">
                      <option value="30">30 minutes</option>
                      <option value="45">45 minutes</option>
                      <option value="60">1 hour</option>
                      <option value="90">1.5 hours</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- AI Optimization Toggle -->
              <div class="form-section">
                <div class="ai-optimization">
                  <label class="switch-container">
                    <input 
                      type="checkbox" 
                      v-model="schedulerForm.aiOptimization"
                    />
                    <span class="switch-slider"></span>
                    <span class="switch-label">
                      <i class="fas fa-robot"></i>
                      AI Optimization
                    </span>
                  </label>
                  <p class="optimization-desc">
                    Let AI analyze your performance patterns and optimize revision intervals
                  </p>
                </div>
              </div>

              <!-- Preview Timeline -->
              <div v-if="schedulerForm.difficulty" class="preview-section">
                <h4>
                  <i class="fas fa-timeline"></i>
                  Revision Timeline Preview
                </h4>
                <div class="timeline-preview">
                  <div 
                    v-for="(revision, index) in previewRevisions" 
                    :key="index"
                    class="timeline-item"
                  >
                    <div class="timeline-dot" :style="{ background: getRevisionColor(index) }">
                      <i class="fas fa-check"></i>
                    </div>
                    <div class="timeline-content">
                      <h5>{{ revision.title }}</h5>
                      <p>{{ revision.date }}</p>
                      <span class="retention-rate">
                        {{ revision.retention }}% expected retention
                      </span>
                    </div>
                    <div class="timeline-line"></div>
                  </div>
                </div>
              </div>

              <button type="submit" class="submit-button-ultra">
                <span class="button-text">
                  <i class="fas fa-rocket"></i>
                  Generate Smart Schedule
                </span>
                <div class="button-energy"></div>
              </button>
            </form>
          </div>

          <!-- Visual Schedule Overview -->
          <div class="schedule-visualization glass-ultra">
            <h3 class="panel-title">
              <i class="fas fa-project-diagram"></i>
              Schedule Visualization
            </h3>
            <div class="visualization-container">
              <canvas ref="scheduleCanvas"></canvas>
              <div class="visualization-controls">
                <button @click="zoomIn" class="viz-control">
                  <i class="fas fa-search-plus"></i>
                </button>
                <button @click="zoomOut" class="viz-control">
                  <i class="fas fa-search-minus"></i>
                </button>
                <button @click="resetView" class="viz-control">
                  <i class="fas fa-compress"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Study Session Section -->
      <section v-if="activeSection === 'study'" class="section-study">
        <div class="study-interface">
          <!-- Active Session Panel -->
          <div class="session-panel glass-ultra" v-if="activeSession">
            <div class="session-header">
              <h3>
                <i class="fas fa-graduation-cap"></i>
                {{ activeSession.subject }}
              </h3>
              <div class="session-timer">
                <span class="timer-value">{{ sessionTimer }}</span>
                <div class="timer-ring">
                  <svg>
                    <circle 
                      cx="40" 
                      cy="40" 
                      r="38"
                      :stroke-dasharray="`${timerProgress * 2.39} 239`"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Study Tools -->
            <div class="study-tools">
              <div class="tool-card" v-for="tool in studyTools" :key="tool.id">
                <button 
                  class="tool-button"
                  :class="{ active: activeTool === tool.id }"
                  @click="activateTool(tool.id)"
                >
                  <i :class="tool.icon"></i>
                  <span>{{ tool.name }}</span>
                </button>
              </div>
            </div>

            <!-- Focus Mode -->
            <div class="focus-mode" v-if="focusMode">
              <div class="focus-content">
                <div class="breathing-guide">
                  <div class="breath-circle"></div>
                  <p>{{ breathingPhase }}</p>
                </div>
                <div class="focus-stats">
                  <div class="stat">
                    <span class="stat-value">{{ focusScore }}</span>
                    <span class="stat-label">Focus Score</span>
                  </div>
                  <div class="stat">
                    <span class="stat-value">{{ productivityRate }}%</span>
                    <span class="stat-label">Productivity</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Session Controls -->
            <div class="session-controls">
              <button @click="pauseSession" class="control-btn">
                <i class="fas fa-pause"></i>
                Pause
              </button>
              <button @click="completeSession" class="control-btn primary">
                <i class="fas fa-check"></i>
                Complete
              </button>
              <button @click="endSession" class="control-btn danger">
                <i class="fas fa-stop"></i>
                End
              </button>
            </div>
          </div>

          <!-- Session History -->
          <div class="history-panel glass-ultra">
            <h3 class="panel-title">
              <i class="fas fa-history"></i>
              Recent Sessions
            </h3>
            <div class="history-list">
              <div 
                v-for="session in sessionHistory" 
                :key="session.id"
                class="history-item"
              >
                <div class="session-icon" :style="{ background: session.color }">
                  <i :class="session.icon"></i>
                </div>
                <div class="session-info">
                  <h4>{{ session.subject }}</h4>
                  <p>{{ session.date }} • {{ session.duration }}</p>
                  <div class="session-metrics">
                    <span class="metric">
                      <i class="fas fa-fire"></i>
                      {{ session.score }}
                    </span>
                    <span class="metric">
                      <i class="fas fa-check"></i>
                      {{ session.completion }}%
                    </span>
                  </div>
                </div>
                <button class="review-btn" @click="reviewSession(session)">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Reports Section -->
      <section v-if="activeSection === 'reports'" class="section-reports">
        <div class="reports-container">
          <!-- Report Generator -->
          <div class="report-generator glass-ultra">
            <h3 class="panel-title">
              <i class="fas fa-file-chart-line"></i>
              Generate Reports
            </h3>
            <div class="generator-options">
              <div class="option-group">
                <label>Report Type</label>
                <select v-model="reportOptions.type" class="ultra-select">
                  <option value="performance">Performance Analysis</option>
                  <option value="progress">Progress Report</option>
                  <option value="prediction">Predictive Analysis</option>
                  <option value="comprehensive">Comprehensive Review</option>
                </select>
              </div>
              <div class="option-group">
                <label>Time Period</label>
                <select v-model="reportOptions.period" class="ultra-select">
                  <option value="week">Last Week</option>
                  <option value="month">Last Month</option>
                  <option value="quarter">Last Quarter</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>
              <div class="option-group" v-if="reportOptions.period === 'custom'">
                <label>Date Range</label>
                <div class="date-range">
                  <input type="date" v-model="reportOptions.startDate" class="ultra-input" />
                  <span>to</span>
                  <input type="date" v-model="reportOptions.endDate" class="ultra-input" />
                </div>
              </div>
              <button @click="generateReport" class="generate-btn">
                <i class="fas fa-cog"></i>
                Generate Report
                <div class="btn-spinner" v-if="generatingReport"></div>
              </button>
            </div>
          </div>

          <!-- Generated Reports -->
          <div class="reports-display glass-ultra" v-if="generatedReports.length">
            <h3 class="panel-title">
              <i class="fas fa-folder-open"></i>
              Generated Reports
            </h3>
            <div class="reports-grid">
              <div 
                v-for="report in generatedReports" 
                :key="report.id"
                class="report-card"
              >
                <div class="report-header">
                  <div class="report-icon">
                    <i :class="getReportIcon(report.type)"></i>
                  </div>
                  <div class="report-meta">
                    <h4>{{ report.title }}</h4>
                    <p>{{ report.generated }}</p>
                  </div>
                </div>
                <div class="report-preview">
                  <div class="preview-chart">
                    <canvas :ref="`report-${report.id}`"></canvas>
                  </div>
                  <div class="preview-stats">
                    <div class="stat" v-for="stat in report.keyStats" :key="stat.label">
                      <span class="label">{{ stat.label }}</span>
                      <span class="value">{{ stat.value }}</span>
                    </div>
                  </div>
                </div>
                <div class="report-actions">
                  <button @click="viewReport(report)" class="action-btn">
                    <i class="fas fa-eye"></i>
                    View
                  </button>
                  <button @click="downloadReport(report)" class="action-btn">
                    <i class="fas fa-download"></i>
                    Export
                  </button>
                  <button @click="shareReport(report)" class="action-btn">
                    <i class="fas fa-share"></i>
                    Share
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Gamification Overlay -->
      <transition name="achievement-popup">
        <div v-if="showAchievement" class="achievement-popup">
          <div class="achievement-content">
            <div class="achievement-icon-large">
              <i :class="currentAchievement.icon"></i>
              <div class="icon-effects">
                <div class="effect-ring"></div>
                <div class="effect-particles">
                  <span v-for="i in 8" :key="i"></span>
                </div>
              </div>
            </div>
            <h2>Achievement Unlocked!</h2>
            <h3>{{ currentAchievement.name }}</h3>
            <p>{{ currentAchievement.description }}</p>
            <div class="achievement-rewards">
              <div class="reward" v-for="reward in currentAchievement.rewards" :key="reward.type">
                <i :class="reward.icon"></i>
                <span>+{{ reward.value }} {{ reward.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- Floating Action Menu -->
    <div class="floating-menu" :class="{ expanded: menuExpanded }">
      <button class="menu-toggle" @click="menuExpanded = !menuExpanded">
        <i class="fas fa-plus"></i>
      </button>
      <div class="menu-items">
        <button 
          v-for="action in floatingActions" 
          :key="action.id"
          class="menu-item"
          @click="executeAction(action.id)"
          :title="action.label"
        >
          <i :class="action.icon"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useStore } from 'vuex'
import { format, addDays } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { 
  Chart, 
  NeuralNetwork3D, 
  ScheduleVisualization, 
  generateHeatmapData,
  calculatePerformanceMetrics 
} from '@/utils/chartUtils'

export default {
  name: 'RevisionSystemUltra',
  setup() {
    const store = useStore()
    
    // Refs
    const particleCanvas = ref(null)
    const performanceChart = ref(null)
    const scheduleCanvas = ref(null)
    
    // State
    const activeSection = ref('dashboard')
    const menuExpanded = ref(false)
    const showAchievement = ref(false)
    const currentAchievement = ref({})
    const focusMode = ref(false)
    const activeTool = ref(null)
    const activeSession = ref(null)
    const generatingReport = ref(false)
    
    // Navigation
    const navigationTabs = [
      { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt', badge: null },
      { id: 'scheduler', label: 'Scheduler', icon: 'fas fa-calendar-plus', badge: '3' },
      { id: 'study', label: 'Study', icon: 'fas fa-graduation-cap', badge: null },
      { id: 'reports', label: 'Reports', icon: 'fas fa-chart-line', badge: 'NEW' }
    ]
    
    // Taglines for typewriter effect
    const taglines = [
      'Revolutionizing Medical Education',
      'AI-Powered Learning Optimization',
      'Master Medicine with Science',
      'Your Path to Excellence'
    ]
    const currentTagline = ref(taglines[0])
    let taglineIndex = 0
    
    // Live Stats
    const liveStats = ref([
      { id: 'retention', label: 'Retention Rate', value: '87%', color: '#6366f1' },
      { id: 'streak', label: 'Study Streak', value: '15d', color: '#8b5cf6' },
      { id: 'mastery', label: 'Mastery Level', value: '72%', color: '#a855f7' },
      { id: 'efficiency', label: 'Efficiency', value: '94%', color: '#6366f1' }
    ])
    
    // Performance Metrics
    const performanceMetrics = ref([
      { id: 'accuracy', label: 'Accuracy', value: '89%', icon: 'fas fa-bullseye', color: '#22c55e', trend: 'up', change: 5 },
      { id: 'speed', label: 'Speed', value: '1.2x', icon: 'fas fa-tachometer-alt', color: '#f59e0b', trend: 'up', change: 8 },
      { id: 'consistency', label: 'Consistency', value: '92%', icon: 'fas fa-sync', color: '#6366f1', trend: 'up', change: 3 },
      { id: 'focus', label: 'Focus Time', value: '45m', icon: 'fas fa-brain', color: '#8b5cf6', trend: 'down', change: 2 }
    ])
    
    // AI Insights
    const aiInsights = ref([
      {
        id: 1,
        type: 'optimization',
        icon: 'fas fa-lightbulb',
        title: 'Optimal Study Window Detected',
        description: 'Your performance peaks between 8-11 AM. Schedule important revisions during this time.',
        priority: 'high',
        action: { label: 'Adjust Schedule', handler: 'optimizeSchedule' }
      },
      {
        id: 2,
        type: 'warning',
        icon: 'fas fa-exclamation-triangle',
        title: 'Forgetting Curve Alert',
        description: 'Pharmacology revision overdue by 3 days. Risk of significant retention loss.',
        priority: 'critical',
        action: { label: 'Review Now', handler: 'startUrgentReview' }
      },
      {
        id: 3,
        type: 'suggestion',
        icon: 'fas fa-magic',
        title: 'Learning Pattern Insight',
        description: 'Visual learning techniques show 23% better retention for Anatomy topics.',
        priority: 'medium',
        action: { label: 'Enable Visual Mode', handler: 'enableVisualMode' }
      }
    ])
    
    // Achievements
    const achievements = ref([
      {
        id: 'streak_master',
        name: 'Streak Master',
        description: 'Maintain a 30-day study streak',
        icon: 'fas fa-fire',
        progress: 50,
        unlocked: false
      },
      {
        id: 'speed_demon',
        name: 'Speed Demon',
        description: 'Complete 100 reviews under time limit',
        icon: 'fas fa-bolt',
        progress: 73,
        unlocked: false
      },
      {
        id: 'perfectionist',
        name: 'Perfectionist',
        description: 'Achieve 100% accuracy in 10 sessions',
        icon: 'fas fa-star',
        progress: 30,
        unlocked: false
      },
      {
        id: 'knowledge_seeker',
        name: 'Knowledge Seeker',
        description: 'Study all available subjects',
        icon: 'fas fa-book',
        progress: 85,
        unlocked: true
      }
    ])
    
    // Scheduler Form
    const schedulerForm = ref({
      subject: '',
      difficulty: '',
      style: 'balanced',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      preferredTime: '09:00',
      duration: 45,
      aiOptimization: true
    })
    
    // Difficulty Levels
    const difficultyLevels = [
      {
        id: 'easy',
        name: 'Easy',
        icon: 'fas fa-smile',
        description: 'Quick to grasp, minimal review needed',
        intervals: [2, 7, 21, 60],
        color: '#22c55e'
      },
      {
        id: 'medium',
        name: 'Medium',
        icon: 'fas fa-meh',
        description: 'Moderate complexity, standard spacing',
        intervals: [1, 3, 7, 14, 30],
        color: '#f59e0b'
      },
      {
        id: 'hard',
        name: 'Hard',
        icon: 'fas fa-frown',
        description: 'Complex topic, frequent reviews',
        intervals: [1, 2, 4, 7, 14, 21],
        color: '#ef4444'
      },
      {
        id: 'expert',
        name: 'Expert',
        icon: 'fas fa-dizzy',
        description: 'Highly complex, intensive schedule',
        intervals: [1, 1, 2, 3, 5, 8, 13],
        color: '#8b5cf6'
      }
    ]
    
    // Learning Styles
    const learningStyles = [
      { id: 'visual', name: 'Visual', icon: 'fas fa-eye' },
      { id: 'auditory', name: 'Auditory', icon: 'fas fa-headphones' },
      { id: 'kinesthetic', name: 'Kinesthetic', icon: 'fas fa-hand-paper' },
      { id: 'balanced', name: 'Balanced', icon: 'fas fa-balance-scale' }
    ]
    
    // Study Tools
    const studyTools = [
      { id: 'flashcards', name: 'Flashcards', icon: 'fas fa-layer-group' },
      { id: 'mindmap', name: 'Mind Map', icon: 'fas fa-project-diagram' },
      { id: 'quiz', name: 'Quiz Mode', icon: 'fas fa-question-circle' },
      { id: 'notes', name: 'Smart Notes', icon: 'fas fa-sticky-note' }
    ]
    
    // Floating Actions
    const floatingActions = [
      { id: 'quick-review', label: 'Quick Review', icon: 'fas fa-bolt' },
      { id: 'voice-note', label: 'Voice Note', icon: 'fas fa-microphone' },
      { id: 'screenshot', label: 'Screenshot', icon: 'fas fa-camera' },
      { id: 'ai-chat', label: 'AI Assistant', icon: 'fas fa-robot' }
    ]
    
    // Report Options
    const reportOptions = ref({
      type: 'performance',
      period: 'month',
      startDate: '',
      endDate: ''
    })
    
    // Computed
    const navIndicatorStyle = computed(() => {
      const index = navigationTabs.findIndex(tab => tab.id === activeSection.value)
      return {
        transform: `translateX(${index * 100}%)`
      }
    })
    
    const overallScore = computed(() => {
      // Calculate overall performance score
      return 85
    })
    
    const sessionTimer = computed(() => {
      // Format session timer
      return '23:45'
    })
    
    const timerProgress = computed(() => {
      // Calculate timer progress percentage
      return 65
    })
    
    const focusScore = computed(() => {
      // Calculate focus score
      return 92
    })
    
    const productivityRate = computed(() => {
      // Calculate productivity rate
      return 87
    })
    
    const breathingPhase = computed(() => {
      // Current breathing phase
      return 'Breathe In... 4s'
    })
    
    const today = computed(() => format(new Date(), 'yyyy-MM-dd'))
    
    const previewRevisions = computed(() => {
      if (!schedulerForm.value.difficulty) return []
      
      const level = difficultyLevels.find(l => l.id === schedulerForm.value.difficulty)
      if (!level) return []
      
      const baseDate = new Date(schedulerForm.value.startDate)
      
      return level.intervals.map((days, index) => {
        const revisionDate = addDays(baseDate, days)
        const retention = calculateRetention(days, index)
        
        return {
          title: `Revision ${index + 1}`,
          date: format(revisionDate, "dd 'de' MMMM", { locale: ptBR }),
          retention
        }
      })
    })
    
    // Session History
    const sessionHistory = ref([
      {
        id: 1,
        subject: 'Cardiology',
        date: '2024-01-15',
        duration: '45 min',
        score: 92,
        completion: 100,
        icon: 'fas fa-heart',
        color: '#ef4444'
      },
      {
        id: 2,
        subject: 'Neurology',
        date: '2024-01-14',
        duration: '30 min',
        score: 87,
        completion: 85,
        icon: 'fas fa-brain',
        color: '#8b5cf6'
      }
    ])
    
    // Generated Reports
    const generatedReports = ref([])
    
    // Subject Suggestions
    const subjectSuggestions = ref([])
    
    // Months for heatmap
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    
    // Methods
    // Chart instances
    let neuralNetwork = null
    let performanceChartInstance = null
    let scheduleViz = null
    
    const initializeParticleSystem = () => {
      if (!particleCanvas.value) return
      
      // Initialize 3D neural network visualization
      neuralNetwork = new NeuralNetwork3D(particleCanvas.value)
    }
    
    const initializeCharts = () => {
      // Initialize performance chart
      if (performanceChart.value) {
        const ctx = performanceChart.value.getContext('2d')
        // Chart implementation here
      }
      
      // Initialize schedule visualization
      if (scheduleCanvas.value) {
        const ctx = scheduleCanvas.value.getContext('2d')
        // Schedule visualization implementation here
      }
    }
    
    const cycleTaglines = () => {
      setInterval(() => {
        taglineIndex = (taglineIndex + 1) % taglines.length
        currentTagline.value = taglines[taglineIndex]
      }, 3000)
    }
    
    const getHeatmapIntensity = (week, day) => {
      // Simulate heatmap data
      return Math.floor(Math.random() * 5)
    }
    
    const showDayTooltip = (event, week, day) => {
      // Show tooltip for heatmap cell
    }
    
    const hideDayTooltip = () => {
      // Hide tooltip
    }
    
    const executeInsightAction = (insight) => {
      // Execute AI insight action
      console.log('Executing insight action:', insight.action.handler)
    }
    
    const scheduleRevisions = () => {
      // Schedule revisions based on form data
      console.log('Scheduling revisions:', schedulerForm.value)
    }
    
    const getSuggestions = () => {
      // Get AI suggestions for subjects
      if (schedulerForm.value.subject.length > 2) {
        subjectSuggestions.value = [
          'Cardiology - Heart Failure',
          'Cardiology - Arrhythmias',
          'Cardiology - Coronary Disease'
        ]
      } else {
        subjectSuggestions.value = []
      }
    }
    
    const useAISuggestion = () => {
      // Use AI to suggest next topic
      schedulerForm.value.subject = 'Neurology - Stroke Management'
    }
    
    const calculateRetention = (days, revisionNumber) => {
      // Calculate expected retention based on spaced repetition algorithm
      const base = 100
      const decay = Math.exp(-days / 30)
      const boost = revisionNumber * 10
      return Math.round(base * decay + boost)
    }
    
    const getRevisionColor = (index) => {
      const colors = ['#6366f1', '#8b5cf6', '#a855f7', '#c084fc', '#e9d5ff']
      return colors[index % colors.length]
    }
    
    const activateTool = (toolId) => {
      activeTool.value = toolId
    }
    
    const pauseSession = () => {
      // Pause current study session
    }
    
    const completeSession = () => {
      // Complete current study session
      showAchievementPopup({
        name: 'Session Champion',
        description: 'Completed a perfect study session!',
        icon: 'fas fa-trophy',
        rewards: [
          { type: 'xp', value: 100, label: 'XP', icon: 'fas fa-star' },
          { type: 'streak', value: 1, label: 'Streak', icon: 'fas fa-fire' }
        ]
      })
    }
    
    const endSession = () => {
      // End current study session
      activeSession.value = null
    }
    
    const reviewSession = (session) => {
      // Review past session
      console.log('Reviewing session:', session)
    }
    
    const generateReport = () => {
      generatingReport.value = true
      
      setTimeout(() => {
        generatedReports.value.push({
          id: Date.now(),
          type: reportOptions.value.type,
          title: `${reportOptions.value.type} Report`,
          generated: format(new Date(), 'dd/MM/yyyy HH:mm'),
          keyStats: [
            { label: 'Avg Score', value: '87%' },
            { label: 'Total Hours', value: '124h' },
            { label: 'Topics Covered', value: '42' }
          ]
        })
        generatingReport.value = false
      }, 2000)
    }
    
    const getReportIcon = (type) => {
      const icons = {
        performance: 'fas fa-chart-line',
        progress: 'fas fa-tasks',
        prediction: 'fas fa-crystal-ball',
        comprehensive: 'fas fa-file-alt'
      }
      return icons[type] || 'fas fa-file'
    }
    
    const viewReport = (report) => {
      // View detailed report
    }
    
    const downloadReport = (report) => {
      // Download report as PDF
    }
    
    const shareReport = (report) => {
      // Share report
    }
    
    const executeAction = (actionId) => {
      console.log('Executing action:', actionId)
      menuExpanded.value = false
    }
    
    const showAchievementPopup = (achievement) => {
      currentAchievement.value = achievement
      showAchievement.value = true
      
      setTimeout(() => {
        showAchievement.value = false
      }, 5000)
    }
    
    const zoomIn = () => {
      // Zoom in schedule visualization
    }
    
    const zoomOut = () => {
      // Zoom out schedule visualization
    }
    
    const resetView = () => {
      // Reset schedule visualization view
    }
    
    // Lifecycle
    onMounted(() => {
      initializeParticleSystem()
      initializeCharts()
      cycleTaglines()
      
      // Simulate live data updates
      setInterval(() => {
        // Update live stats
        liveStats.value[0].value = Math.round(85 + Math.random() * 10) + '%'
        liveStats.value[3].value = Math.round(90 + Math.random() * 10) + '%'
      }, 5000)
    })
    
    onUnmounted(() => {
      // Cleanup
    })
    
    return {
      // Refs
      particleCanvas,
      performanceChart,
      scheduleCanvas,
      
      // State
      activeSection,
      menuExpanded,
      showAchievement,
      currentAchievement,
      focusMode,
      activeTool,
      activeSession,
      generatingReport,
      
      // Data
      navigationTabs,
      currentTagline,
      liveStats,
      performanceMetrics,
      aiInsights,
      achievements,
      schedulerForm,
      difficultyLevels,
      learningStyles,
      studyTools,
      floatingActions,
      reportOptions,
      sessionHistory,
      generatedReports,
      subjectSuggestions,
      months,
      
      // Computed
      navIndicatorStyle,
      overallScore,
      sessionTimer,
      timerProgress,
      focusScore,
      productivityRate,
      breathingPhase,
      today,
      previewRevisions,
      
      // Methods
      getHeatmapIntensity,
      showDayTooltip,
      hideDayTooltip,
      executeInsightAction,
      scheduleRevisions,
      getSuggestions,
      useAISuggestion,
      getRevisionColor,
      activateTool,
      pauseSession,
      completeSession,
      endSession,
      reviewSession,
      generateReport,
      getReportIcon,
      viewReport,
      downloadReport,
      shareReport,
      executeAction,
      zoomIn,
      zoomOut,
      resetView
    }
  }
}
</script>

<style scoped>
/* Global Styles */
.revision-system-ultra {
  min-height: 100vh;
  background: #0a0e1a;
  color: #e2e8f0;
  position: relative;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Ultra Background System */
.ultra-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
}

.particle-system {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

.neural-network-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,...') center/cover;
  opacity: 0.03;
}

.holographic-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.quantum-field {
  position: absolute;
  width: 100%;
  height: 100%;
}

.quantum-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #6366f1;
  border-radius: 50%;
  filter: blur(1px);
  animation: quantum-float 10s infinite ease-in-out;
}

.quantum-particle.q-1 { top: 10%; left: 20%; animation-delay: 0s; }
.quantum-particle.q-2 { top: 30%; left: 80%; animation-delay: 1s; }
.quantum-particle.q-3 { top: 60%; left: 10%; animation-delay: 2s; }
.quantum-particle.q-4 { top: 80%; left: 70%; animation-delay: 3s; }
.quantum-particle.q-5 { top: 45%; left: 50%; animation-delay: 4s; }

@keyframes quantum-float {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.4; }
  25% { transform: translate(30px, -30px) scale(1.5); opacity: 0.8; }
  50% { transform: translate(-20px, 20px) scale(0.8); opacity: 0.6; }
  75% { transform: translate(40px, 10px) scale(1.2); opacity: 0.9; }
}

/* Main Container */
.ultra-container {
  position: relative;
  z-index: 1;
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
}

/* Ultra Header */
.ultra-header {
  margin-bottom: 3rem;
}

.header-hologram {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.8));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 24px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.header-hologram::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(99, 102, 241, 0.6), 
    transparent
  );
  animation: scan-line 3s linear infinite;
}

@keyframes scan-line {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* 3D Logo */
.logo-3d {
  position: relative;
  width: 80px;
  height: 80px;
  perspective: 1000px;
}

.logo-cube {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  animation: cube-rotate 20s infinite linear;
}

@keyframes cube-rotate {
  0% { transform: rotateX(0) rotateY(0); }
  100% { transform: rotateX(360deg) rotateY(360deg); }
}

.cube-face {
  position: absolute;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.3));
  border: 1px solid rgba(99, 102, 241, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #6366f1;
}

.cube-face.front { transform: translateZ(40px); }
.cube-face.back { transform: rotateY(180deg) translateZ(40px); }
.cube-face.right { transform: rotateY(90deg) translateZ(40px); }
.cube-face.left { transform: rotateY(-90deg) translateZ(40px); }
.cube-face.top { transform: rotateX(90deg) translateZ(40px); }
.cube-face.bottom { transform: rotateX(-90deg) translateZ(40px); }

.energy-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  border: 2px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  animation: ring-pulse 2s infinite;
}

@keyframes ring-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
}

/* Brand Text */
.brand-text {
  flex: 1;
}

.ultra-title {
  font-size: 3rem;
  font-weight: 900;
  margin: 0;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  gap: 1rem;
}

.text-hologram {
  background: linear-gradient(135deg, #6366f1, #8b5cf6, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.text-ultra {
  font-size: 1.2rem;
  font-weight: 400;
  letter-spacing: 0.3em;
  color: #64748b;
  animation: text-glow 2s ease-in-out infinite alternate;
}

@keyframes text-glow {
  from { text-shadow: 0 0 10px rgba(139, 92, 246, 0.5); }
  to { text-shadow: 0 0 20px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.6); }
}

.tagline {
  font-size: 1.125rem;
  color: #94a3b8;
  margin: 0.5rem 0 0 0;
  font-family: 'Fira Code', monospace;
}

.typewriter {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  animation: typewriter 3s steps(40) 1s forwards;
}

.cursor {
  display: inline-block;
  animation: cursor-blink 1s infinite;
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Stats Hologram */
.stats-hologram {
  display: flex;
  gap: 2rem;
  justify-content: flex-end;
}

.stat-orb {
  position: relative;
}

.orb-container {
  width: 80px;
  height: 80px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.orb-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  z-index: 2;
  position: relative;
}

.orb-label {
  font-size: 0.75rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
}

.orb-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  filter: blur(20px);
  opacity: 0.4;
  animation: orb-pulse 3s infinite;
}

@keyframes orb-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
}

.orb-ring {
  position: absolute;
  width: 80px;
  height: 80px;
  top: 0;
  left: 0;
}

.orb-ring circle {
  fill: none;
  stroke-width: 2;
  stroke-dasharray: 226;
  stroke-dashoffset: 56;
  stroke-linecap: round;
  animation: ring-rotate 2s linear infinite;
}

@keyframes ring-rotate {
  to { stroke-dashoffset: -170; }
}

/* Navigation */
.ultra-nav {
  margin-top: 2rem;
}

.nav-track {
  display: flex;
  gap: 1rem;
  position: relative;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 16px;
  padding: 0.5rem;
}

.nav-tab {
  flex: 1;
  padding: 1rem 1.5rem;
  background: transparent;
  border: none;
  color: #94a3b8;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  position: relative;
  z-index: 2;
  border-radius: 12px;
}

.nav-tab:hover {
  color: #e2e8f0;
}

.nav-tab.active {
  color: #f1f5f9;
}

.tab-icon {
  font-size: 1.25rem;
}

.tab-badge {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  border-radius: 999px;
  font-weight: 700;
}

.nav-indicator {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: calc(25% - 0.75rem);
  height: calc(100% - 1rem);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.3));
  border: 1px solid rgba(99, 102, 241, 0.4);
  border-radius: 12px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sections */
section {
  animation: section-fade-in 0.5s ease-out;
}

@keyframes section-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

/* Glass Panel */
.glass-ultra {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.7));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 20px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.glass-ultra::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 40%,
    rgba(99, 102, 241, 0.1) 50%,
    transparent 60%
  );
  animation: glass-shine 4s infinite;
}

@keyframes glass-shine {
  0% { transform: translateX(-100%) translateY(-100%); }
  100% { transform: translateX(100%) translateY(100%); }
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-title i {
  color: #6366f1;
}

/* Performance Chart */
.chart-container {
  position: relative;
  height: 250px;
  margin-bottom: 2rem;
}

.chart-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.performance-score {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-value {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.score-label {
  font-size: 0.875rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.metric {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
}

.metric-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 10px;
  font-size: 1.25rem;
}

.metric-data {
  flex: 1;
}

.metric-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: #f1f5f9;
}

.metric-label {
  display: block;
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.125rem;
}

.metric-trend {
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.metric-trend.up {
  color: #22c55e;
}

.metric-trend.down {
  color: #ef4444;
}

/* Heatmap */
.heatmap-container {
  position: relative;
  overflow-x: auto;
}

.heatmap-months {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  padding-left: 2rem;
  font-size: 0.75rem;
  color: #64748b;
}

.heatmap-grid {
  display: flex;
  gap: 3px;
}

.heatmap-week {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.heatmap-cell {
  width: 14px;
  height: 14px;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 3px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
}

.heatmap-cell[data-intensity="0"] { background: rgba(30, 41, 59, 0.5); }
.heatmap-cell[data-intensity="1"] { background: rgba(99, 102, 241, 0.2); }
.heatmap-cell[data-intensity="2"] { background: rgba(99, 102, 241, 0.4); }
.heatmap-cell[data-intensity="3"] { background: rgba(99, 102, 241, 0.6); }
.heatmap-cell[data-intensity="4"] { background: rgba(99, 102, 241, 0.8); }

.heatmap-cell:hover {
  transform: scale(1.2);
  border: 1px solid #6366f1;
}

.cell-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #6366f1;
  border-radius: 50%;
  filter: blur(10px);
  opacity: 0;
  transition: opacity 0.2s;
}

.heatmap-cell:hover .cell-glow {
  opacity: 0.6;
}

.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  font-size: 0.75rem;
  color: #64748b;
}

.legend-cells {
  display: flex;
  gap: 3px;
}

/* AI Insights */
.insights-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  border: 1px solid transparent;
  transition: all 0.3s;
}

.insight-item:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(99, 102, 241, 0.2);
}

.insight-optimization { border-left: 3px solid #6366f1; }
.insight-warning { border-left: 3px solid #f59e0b; }
.insight-suggestion { border-left: 3px solid #8b5cf6; }

.insight-icon {
  position: relative;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  font-size: 1.25rem;
  color: #6366f1;
}

.icon-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid currentColor;
  border-radius: 12px;
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0; }
}

.insight-content {
  flex: 1;
}

.insight-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 0.5rem 0;
}

.insight-content p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 0.75rem 0;
  line-height: 1.5;
}

.insight-action button {
  padding: 0.5rem 1rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  color: #6366f1;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.insight-action button:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateX(4px);
}

.insight-priority {
  display: flex;
  align-items: flex-start;
}

.insight-priority span {
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-high {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.priority-critical {
  background: rgba(239, 68, 68, 0.2);
  color: #dc2626;
  animation: critical-pulse 1s infinite;
}

@keyframes critical-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.priority-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

/* Achievements Grid */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.achievement-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  opacity: 0.5;
  filter: grayscale(1);
  transition: all 0.3s;
}

.achievement-item.unlocked {
  opacity: 1;
  filter: grayscale(0);
}

.achievement-icon {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #6366f1;
}

.progress-ring {
  position: absolute;
  width: 60px;
  height: 60px;
  transform: rotate(-90deg);
}

.progress-ring circle {
  fill: none;
  stroke: #6366f1;
  stroke-width: 3;
  stroke-linecap: round;
  transition: stroke-dasharray 0.5s;
}

.achievement-info h5 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 0.25rem 0;
}

.achievement-info p {
  font-size: 0.75rem;
  color: #94a3b8;
  margin: 0 0 0.5rem 0;
}

.progress-bar {
  height: 4px;
  background: rgba(99, 102, 241, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  transition: width 0.5s;
}

/* Scheduler Section */
.scheduler-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.scheduler-panel,
.schedule-visualization {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.7));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 20px;
  padding: 2rem;
}

.panel-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0 0 2rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.panel-title i {
  color: #6366f1;
}

/* Ultra Form */
.ultra-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-label i {
  color: #6366f1;
}

.input-group {
  display: flex;
  gap: 0.5rem;
}

.ultra-input,
.ultra-select {
  flex: 1;
  padding: 1rem 1.25rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  color: #f1f5f9;
  font-size: 1rem;
  transition: all 0.3s;
}

.ultra-input:focus,
.ultra-select:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(30, 41, 59, 0.8);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.ultra-input::placeholder {
  color: #64748b;
}

.ai-suggest-btn {
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
  border: 1px solid rgba(99, 102, 241, 0.4);
  color: #a5b4fc;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ai-suggest-btn:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(139, 92, 246, 0.3));
  transform: translateY(-2px);
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 8px;
  margin-top: 0.5rem;
  overflow: hidden;
  z-index: 10;
}

.suggestion-item {
  padding: 0.75rem 1rem;
  color: #e2e8f0;
  cursor: pointer;
  transition: background 0.2s;
}

.suggestion-item:hover {
  background: rgba(99, 102, 241, 0.2);
}

/* Difficulty Matrix */
.difficulty-matrix {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.difficulty-cell {
  position: relative;
  padding: 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid transparent;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.difficulty-cell:hover {
  background: rgba(30, 41, 59, 0.7);
  transform: translateY(-2px);
}

.difficulty-cell.selected {
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(99, 102, 241, 0.1);
}

.cell-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.cell-info h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 0.5rem 0;
}

.cell-info p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 1rem 0;
}

.interval-preview {
  display: flex;
  gap: 0.5rem;
}

.interval-preview span {
  padding: 0.25rem 0.5rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 6px;
  font-size: 0.75rem;
  color: #a5b4fc;
}

.cell-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.3;
  pointer-events: none;
}

/* Style Selector */
.style-selector {
  display: flex;
  gap: 1rem;
}

.style-option {
  flex: 1;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid transparent;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.style-option:hover {
  background: rgba(30, 41, 59, 0.7);
}

.style-option.active {
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(99, 102, 241, 0.1);
}

.style-option i {
  font-size: 1.5rem;
  color: #6366f1;
  display: block;
  margin-bottom: 0.5rem;
}

.style-option span {
  font-size: 0.875rem;
  color: #e2e8f0;
}

/* Time Grid */
.time-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.time-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.time-input label {
  font-size: 0.875rem;
  color: #94a3b8;
}

/* AI Optimization Toggle */
.ai-optimization {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
}

.switch-container input {
  display: none;
}

.switch-slider {
  position: relative;
  width: 50px;
  height: 26px;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 13px;
  transition: background 0.3s;
}

.switch-slider::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 20px;
  height: 20px;
  background: #64748b;
  border-radius: 50%;
  transition: all 0.3s;
}

.switch-container input:checked + .switch-slider {
  background: rgba(99, 102, 241, 0.3);
}

.switch-container input:checked + .switch-slider::after {
  left: 27px;
  background: #6366f1;
}

.switch-label {
  font-weight: 600;
  color: #e2e8f0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.optimization-desc {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
}

/* Preview Timeline */
.preview-section {
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
}

.preview-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-section h4 i {
  color: #6366f1;
}

.timeline-preview {
  position: relative;
  padding-left: 2rem;
}

.timeline-item {
  position: relative;
  padding-bottom: 2rem;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -2rem;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  color: white;
}

.timeline-line {
  position: absolute;
  left: -1.5rem;
  top: 16px;
  width: 2px;
  height: calc(100% - 16px);
  background: rgba(99, 102, 241, 0.2);
}

.timeline-item:last-child .timeline-line {
  display: none;
}

.timeline-content h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 0.25rem 0;
}

.timeline-content p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 0.5rem 0;
}

.retention-rate {
  font-size: 0.75rem;
  color: #6366f1;
  font-weight: 500;
}

/* Submit Button Ultra */
.submit-button-ultra {
  position: relative;
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1.125rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.submit-button-ultra:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 40px rgba(99, 102, 241, 0.3);
}

.button-text {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.button-energy {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s;
}

.submit-button-ultra:hover .button-energy {
  opacity: 1;
}

/* Schedule Visualization */
.visualization-container {
  position: relative;
  height: 400px;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 12px;
  overflow: hidden;
}

.visualization-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
}

.viz-control {
  width: 36px;
  height: 36px;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 8px;
  color: #94a3b8;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.viz-control:hover {
  background: rgba(99, 102, 241, 0.2);
  color: #e2e8f0;
}

/* Study Interface */
.study-interface {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.session-panel {
  padding: 2rem;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.session-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.session-timer {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timer-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #f1f5f9;
  z-index: 1;
}

.timer-ring svg {
  position: absolute;
  width: 80px;
  height: 80px;
  transform: rotate(-90deg);
}

.timer-ring circle {
  fill: none;
  stroke: #6366f1;
  stroke-width: 3;
  stroke-linecap: round;
  transition: stroke-dasharray 1s;
}

/* Study Tools */
.study-tools {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.tool-card {
  position: relative;
}

.tool-button {
  width: 100%;
  padding: 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border: 2px solid transparent;
  border-radius: 12px;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.tool-button:hover {
  background: rgba(30, 41, 59, 0.7);
  color: #e2e8f0;
}

.tool-button.active {
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.tool-button i {
  font-size: 2rem;
}

.tool-button span {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Focus Mode */
.focus-mode {
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.focus-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.breathing-guide {
  text-align: center;
}

.breath-circle {
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.3), transparent);
  border: 2px solid rgba(99, 102, 241, 0.5);
  border-radius: 50%;
  margin: 0 auto 1rem;
  animation: breathe 4s infinite;
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.breathing-guide p {
  font-size: 1rem;
  color: #94a3b8;
  margin: 0;
}

.focus-stats {
  display: flex;
  gap: 2rem;
}

.focus-stats .stat {
  text-align: center;
}

.focus-stats .stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #f1f5f9;
}

.focus-stats .stat-label {
  display: block;
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

/* Session Controls */
.session-controls {
  display: flex;
  gap: 1rem;
}

.control-btn {
  flex: 1;
  padding: 0.875rem 1.5rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #94a3b8;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.control-btn:hover {
  background: rgba(30, 41, 59, 0.7);
  color: #e2e8f0;
}

.control-btn.primary {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #6366f1;
}

.control-btn.primary:hover {
  background: rgba(99, 102, 241, 0.2);
}

.control-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.control-btn.danger:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* History Panel */
.history-panel {
  padding: 1.5rem;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  transition: all 0.2s;
}

.history-item:hover {
  background: rgba(30, 41, 59, 0.7);
}

.session-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 1.25rem;
  color: white;
}

.session-info {
  flex: 1;
}

.session-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 0.25rem 0;
}

.session-info p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0 0 0.5rem 0;
}

.session-metrics {
  display: flex;
  gap: 1rem;
}

.session-metrics .metric {
  font-size: 0.75rem;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.review-btn {
  width: 36px;
  height: 36px;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 8px;
  color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.review-btn:hover {
  background: rgba(99, 102, 241, 0.2);
}

/* Reports Section */
.reports-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.report-generator {
  padding: 2rem;
}

.generator-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-range span {
  color: #64748b;
}

.generate-btn {
  grid-column: 1 / -1;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  position: relative;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.btn-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Reports Display */
.reports-display {
  padding: 2rem;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.report-card {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s;
}

.report-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.report-header {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(99, 102, 241, 0.05);
  border-bottom: 1px solid rgba(99, 102, 241, 0.2);
}

.report-icon {
  width: 48px;
  height: 48px;
  background: rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: #6366f1;
}

.report-meta h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0 0 0.25rem 0;
}

.report-meta p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
}

.report-preview {
  padding: 1.5rem;
}

.preview-chart {
  height: 150px;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.preview-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.preview-stats .stat {
  text-align: center;
}

.preview-stats .label {
  display: block;
  font-size: 0.75rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.preview-stats .value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-top: 0.25rem;
}

.report-actions {
  display: flex;
  border-top: 1px solid rgba(99, 102, 241, 0.1);
}

.action-btn {
  flex: 1;
  padding: 1rem;
  background: transparent;
  border: none;
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.action-btn:hover {
  background: rgba(99, 102, 241, 0.05);
  color: #6366f1;
}

.action-btn + .action-btn {
  border-left: 1px solid rgba(99, 102, 241, 0.1);
}

/* Achievement Popup */
.achievement-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  pointer-events: none;
}

.achievement-content {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  backdrop-filter: blur(20px);
  border: 2px solid rgba(99, 102, 241, 0.4);
  border-radius: 24px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.achievement-icon-large {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: #f59e0b;
}

.icon-effects {
  position: absolute;
  width: 100%;
  height: 100%;
}

.effect-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border: 3px solid #f59e0b;
  border-radius: 50%;
  animation: ring-expand 1s ease-out;
}

@keyframes ring-expand {
  from {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  to {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

.effect-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.effect-particles span {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #f59e0b;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  animation: particle-burst 1s ease-out forwards;
}

.effect-particles span:nth-child(1) { --angle: 0deg; }
.effect-particles span:nth-child(2) { --angle: 45deg; }
.effect-particles span:nth-child(3) { --angle: 90deg; }
.effect-particles span:nth-child(4) { --angle: 135deg; }
.effect-particles span:nth-child(5) { --angle: 180deg; }
.effect-particles span:nth-child(6) { --angle: 225deg; }
.effect-particles span:nth-child(7) { --angle: 270deg; }
.effect-particles span:nth-child(8) { --angle: 315deg; }

@keyframes particle-burst {
  0% {
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(80px);
    opacity: 0;
  }
}

.achievement-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f59e0b;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.achievement-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0 0 1rem 0;
}

.achievement-content p {
  font-size: 1.125rem;
  color: #94a3b8;
  margin: 0 0 2rem 0;
}

.achievement-rewards {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.reward {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #6366f1;
}

.reward i {
  font-size: 1.25rem;
}

/* Achievement Popup Transitions */
.achievement-popup-enter-active,
.achievement-popup-leave-active {
  transition: all 0.5s ease;
}

.achievement-popup-enter-from {
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
}

.achievement-popup-leave-to {
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
}

/* Floating Action Menu */
.floating-menu {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 999;
}

.menu-toggle {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.menu-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 15px 40px rgba(99, 102, 241, 0.4);
}

.floating-menu.expanded .menu-toggle {
  transform: rotate(45deg);
}

.menu-items {
  position: absolute;
  bottom: 80px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  opacity: 0;
  transform: scale(0.8) translateY(20px);
  transition: all 0.3s;
  pointer-events: none;
}

.floating-menu.expanded .menu-items {
  opacity: 1;
  transform: scale(1) translateY(0);
  pointer-events: all;
}

.menu-item {
  width: 48px;
  height: 48px;
  background: rgba(15, 23, 42, 0.9);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  color: #94a3b8;
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-item:hover {
  background: rgba(99, 102, 241, 0.2);
  color: #6366f1;
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .ultra-container {
    padding: 1rem;
  }
  
  .scheduler-container {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .study-interface {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .ultra-title {
    font-size: 2rem;
  }
  
  .brand-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stats-hologram {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .nav-track {
    flex-wrap: wrap;
  }
  
  .difficulty-matrix {
    grid-template-columns: 1fr;
  }
  
  .time-grid {
    grid-template-columns: 1fr;
  }
  
  .style-selector {
    flex-direction: column;
  }
  
  .study-tools {
    grid-template-columns: 1fr;
  }
  
  .reports-grid {
    grid-template-columns: 1fr;
  }
}
</style>